import json
import logging
from datetime import datetime
from typing import Optional, List

from app.events.kafka_client import get_producer
from app.schemas.events import (
    UserCreatedEvent,
    UserUpdatedEvent,
    UserDeletedEvent,
    UserLoginEvent,
    UserLogoutEvent,
    UserPasswordChangedEvent
)

logger = logging.getLogger(__name__)

async def publish_user_created_event(
    user_id: int,
    email: str,
    first_name: str,
    last_name: str,
    status: bool = True,
    action: Optional[str] = None
):
    """
    Publish a user_created event to Kafka

    Args:
        user_id: The ID of the created user
        email: User's email address
        first_name: User's first name
        last_name: User's last name
        status: Whether the user is active
        action: User action field
    """
    try:
        # Create event payload
        event = UserCreatedEvent(
            user_id=user_id,
            timestamp=datetime.utcnow(),
            email=email,
            first_name=first_name,
            last_name=last_name,
            status=status,
            action=action
        )
        
        # Serialize to JSON
        event_json = event.model_dump_json()
        
        # Get Kafka producer
        producer = await get_producer()
        
        # Send message to <PERSON>fka topic
        await producer.send_and_wait(
            topic="user-events",
            value=event_json.encode("utf-8"),
            key=str(user_id).encode("utf-8")
        )
        
        logger.info(f"Published user_created event for user {user_id}")
    except Exception as e:
        logger.error(f"Failed to publish user_created event: {str(e)}")

async def publish_user_updated_event(
    user_id: int,
    updated_fields: List[str],
    email: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    status: Optional[bool] = None,
    action: Optional[str] = None
):
    """
    Publish a user_updated event to Kafka

    Args:
        user_id: The ID of the updated user
        updated_fields: List of fields that were updated
        email: Updated email (if changed)
        first_name: Updated first name (if changed)
        last_name: Updated last name (if changed)
        status: Updated status (if changed)
        action: Updated action (if changed)
    """
    try:
        # Create event payload
        event = UserUpdatedEvent(
            user_id=user_id,
            timestamp=datetime.utcnow(),
            email=email,
            first_name=first_name,
            last_name=last_name,
            status=status,
            action=action,
            updated_fields=updated_fields
        )
        
        # Serialize to JSON
        event_json = event.model_dump_json()
        
        # Get Kafka producer
        producer = await get_producer()
        
        # Send message to Kafka topic
        await producer.send_and_wait(
            topic="user-events",
            value=event_json.encode("utf-8"),
            key=str(user_id).encode("utf-8")
        )
        
        logger.info(f"Published user_updated event for user {user_id}")
    except Exception as e:
        logger.error(f"Failed to publish user_updated event: {str(e)}")

async def publish_user_deleted_event(user_id: int, email: str):
    """
    Publish a user_deleted event to Kafka
    
    Args:
        user_id: The ID of the deleted user
        email: Email of the deleted user
    """
    try:
        # Create event payload
        event = UserDeletedEvent(
            user_id=user_id,
            timestamp=datetime.utcnow(),
            email=email
        )
        
        # Serialize to JSON
        event_json = event.model_dump_json()
        
        # Get Kafka producer
        producer = await get_producer()
        
        # Send message to Kafka topic
        await producer.send_and_wait(
            topic="user-events",
            value=event_json.encode("utf-8"),
            key=str(user_id).encode("utf-8")
        )
        
        logger.info(f"Published user_deleted event for user {user_id}")
    except Exception as e:
        logger.error(f"Failed to publish user_deleted event: {str(e)}")

async def publish_user_login_event(
    user_id: int,
    email: str,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None
):
    """
    Publish a user_login event to Kafka
    
    Args:
        user_id: The ID of the user who logged in
        email: Email of the user
        ip_address: IP address of the login
        user_agent: User agent string
    """
    try:
        # Create event payload
        event = UserLoginEvent(
            user_id=user_id,
            timestamp=datetime.utcnow(),
            email=email,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        # Serialize to JSON
        event_json = event.model_dump_json()
        
        # Get Kafka producer
        producer = await get_producer()
        
        # Send message to Kafka topic
        await producer.send_and_wait(
            topic="user-events",
            value=event_json.encode("utf-8"),
            key=str(user_id).encode("utf-8")
        )
        
        logger.info(f"Published user_login event for user {user_id}")
    except Exception as e:
        logger.error(f"Failed to publish user_login event: {str(e)}")

async def publish_user_logout_event(
    user_id: int,
    email: str,
    session_duration: Optional[int] = None
):
    """
    Publish a user_logout event to Kafka
    
    Args:
        user_id: The ID of the user who logged out
        email: Email of the user
        session_duration: Duration of the session in seconds
    """
    try:
        # Create event payload
        event = UserLogoutEvent(
            user_id=user_id,
            timestamp=datetime.utcnow(),
            email=email,
            session_duration=session_duration
        )
        
        # Serialize to JSON
        event_json = event.model_dump_json()
        
        # Get Kafka producer
        producer = await get_producer()
        
        # Send message to Kafka topic
        await producer.send_and_wait(
            topic="user-events",
            value=event_json.encode("utf-8"),
            key=str(user_id).encode("utf-8")
        )
        
        logger.info(f"Published user_logout event for user {user_id}")
    except Exception as e:
        logger.error(f"Failed to publish user_logout event: {str(e)}")

async def publish_user_password_changed_event(user_id: int, email: str):
    """
    Publish a user_password_changed event to Kafka
    
    Args:
        user_id: The ID of the user who changed password
        email: Email of the user
    """
    try:
        # Create event payload
        event = UserPasswordChangedEvent(
            user_id=user_id,
            timestamp=datetime.utcnow(),
            email=email
        )
        
        # Serialize to JSON
        event_json = event.model_dump_json()
        
        # Get Kafka producer
        producer = await get_producer()
        
        # Send message to Kafka topic
        await producer.send_and_wait(
            topic="user-events",
            value=event_json.encode("utf-8"),
            key=str(user_id).encode("utf-8")
        )
        
        logger.info(f"Published user_password_changed event for user {user_id}")
    except Exception as e:
        logger.error(f"Failed to publish user_password_changed event: {str(e)}")
