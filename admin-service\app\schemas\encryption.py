from pydantic import BaseModel, Field
from typing import Dict, Optional

class EncryptionRequest(BaseModel):
    """Request schema for encrypting data"""
    data: str = Field(..., description="Data to encrypt")
    public_key_pem: Optional[str] = Field(None, description="Optional public key PEM. If not provided, server's public key will be used")

class EncryptionResponse(BaseModel):
    """Response schema for encrypted data"""
    encrypted_data: str = Field(..., description="Base64 encoded encrypted data")
    encrypted_key: str = Field(..., description="Base64 encoded encrypted AES key")
    iv: str = Field(..., description="Base64 encoded initialization vector")
    algorithm: str = Field(..., description="Encryption algorithm used")
    key_algorithm: str = Field(..., description="Key encryption algorithm used")

class DecryptionRequest(BaseModel):
    """Request schema for decrypting data"""
    encrypted_data: str = Field(..., description="Base64 encoded encrypted data")
    encrypted_key: str = Field(..., description="Base64 encoded encrypted AES key")
    iv: str = Field(..., description="Base64 encoded initialization vector")
    algorithm: str = Field(..., description="Encryption algorithm used")
    key_algorithm: str = Field(..., description="Key encryption algorithm used")
    private_key_pem: Optional[str] = Field(None, description="Optional private key PEM. If not provided, server's private key will be used")

class DecryptionResponse(BaseModel):
    """Response schema for decrypted data"""
    data: str = Field(..., description="Decrypted data")

class PublicKeyResponse(BaseModel):
    """Response schema for public key"""
    public_key_pem: str = Field(..., description="Public key in PEM format")

class KeyPairResponse(BaseModel):
    """Response schema for key pair generation"""
    private_key_pem: str = Field(..., description="Private key in PEM format")
    public_key_pem: str = Field(..., description="Public key in PEM format")

class EncryptedFieldUpdate(BaseModel):
    """Schema for updating encrypted fields"""
    field_name: str = Field(..., description="Name of the field to update")
    encrypted_value: str = Field(..., description="Encrypted value as JSON string")

class BulkEncryptionRequest(BaseModel):
    """Request schema for bulk encryption"""
    data_items: Dict[str, str] = Field(..., description="Dictionary of key-value pairs to encrypt")
    public_key_pem: Optional[str] = Field(None, description="Optional public key PEM")

class BulkEncryptionResponse(BaseModel):
    """Response schema for bulk encryption"""
    encrypted_items: Dict[str, EncryptionResponse] = Field(..., description="Dictionary of encrypted items")

class BulkDecryptionRequest(BaseModel):
    """Request schema for bulk decryption"""
    encrypted_items: Dict[str, DecryptionRequest] = Field(..., description="Dictionary of encrypted items to decrypt")
    private_key_pem: Optional[str] = Field(None, description="Optional private key PEM")

class BulkDecryptionResponse(BaseModel):
    """Response schema for bulk decryption"""
    decrypted_items: Dict[str, str] = Field(..., description="Dictionary of decrypted items")
