# 📚 ZCare Platform API Documentation

## 🔗 Swagger Documentation URLs

### 🏢 Admin Service (Direct Access - Port 8001)
- **Swagger UI:** http://localhost:8001/docs
- **ReDoc:** http://localhost:8001/redoc
- **OpenAPI JSON:** http://localhost:8001/openapi.json
- **Health Check:** http://localhost:8001/health

### 🔐 Auth Service (Direct Access - Port 8002)
- **Swagger UI:** http://localhost:8002/docs
- **ReDoc:** http://localhost:8002/redoc
- **OpenAPI JSON:** http://localhost:8002/openapi.json
- **Health Check:** http://localhost:8002/health

### 🌐 Gateway Access (Hybrid Setup)
- **Admin via Envoy:** http://localhost:8081/docs (when hybrid gateway is running)
- **Auth via APISIX:** http://localhost:8080/docs (when hybrid gateway is running)

---

## 🏢 Admin Service API Endpoints

### 🔐 Encryption Endpoints
```http
GET  /api/v1/admin/encryption/public-key
POST /api/v1/admin/encryption/encrypt
POST /api/v1/admin/encryption/decrypt
```

### 🌐 Domain Management
```http
POST /api/v1/admin/domains/create_domain/
GET  /api/v1/admin/domains/get_all_domains/
GET  /api/v1/admin/domains/get_domain/{domain_id}
PUT  /api/v1/admin/domains/update_domain/{domain_id}
DELETE /api/v1/admin/domains/delete_domain/{domain_id}
```

### 📱 Application Management
```http
POST /api/v1/admin/applications/create_application/
GET  /api/v1/admin/applications/get_all_applications/
GET  /api/v1/admin/applications/get_application/{app_id}
PUT  /api/v1/admin/applications/update_application/{app_id}
DELETE /api/v1/admin/applications/delete_application/{app_id}
```

### 🏥 Health Check
```http
GET /health
```

---

## 🔐 Auth Service API Endpoints

### 🔑 Authentication
```http
POST /api/v1/auth/login
POST /api/v1/auth/verify-token
POST /api/v1/auth/refresh-token
POST /api/v1/auth/logout
```

### 👤 User Management
```http
GET  /api/v1/auth/users/me
POST /api/v1/auth/users/
GET  /api/v1/auth/users/{user_id}
PUT  /api/v1/auth/users/{user_id}
DELETE /api/v1/auth/users/{user_id}
GET  /api/v1/auth/users/
```

### 🔐 Encryption Endpoints
```http
GET  /api/v1/auth/encryption/public-key
POST /api/v1/auth/encryption/encrypt
POST /api/v1/auth/encryption/decrypt
```

### 🏥 Health Check
```http
GET /health
```

---

## 🧪 Quick API Testing

### Test Admin Service (Direct - Port 8001)
```bash
# Health check
curl http://localhost:8001/health

# Get public key
curl http://localhost:8001/api/v1/admin/encryption/public-key

# Get all domains
curl http://localhost:8001/api/v1/admin/domains/get_all_domains/
```

### Test Auth Service (Direct - Port 8002)
```bash
# Health check
curl http://localhost:8002/health

# Get public key
curl http://localhost:8002/api/v1/auth/encryption/public-key

# Get current user (requires authentication)
curl http://localhost:8002/api/v1/auth/users/me
```

### Test via Gateways (when hybrid setup is running)
```bash
# Admin via Envoy Gateway
curl http://localhost:8081/health
curl http://localhost:8081/api/v1/admin/encryption/public-key

# Auth via APISIX Gateway
curl http://localhost:8080/health
curl http://localhost:8080/api/v1/auth/encryption/public-key
```

---

## 📊 Management Interfaces

### Envoy Admin Interface
- **URL:** http://localhost:9901
- **Features:** 
  - Service health status
  - Configuration dump
  - Statistics and metrics
  - Cluster information

### APISIX Metrics (when available)
- **URL:** http://localhost:9091
- **Features:**
  - Gateway metrics
  - Route statistics
  - Performance monitoring

---

## 🔧 Service Status

### ✅ Currently Working
- **Admin Service:** Fully operational via Envoy (Port 8081)
- **Auth Service Backend:** Healthy and running
- **Swagger Documentation:** Available for both services

### ⚠️ Known Issues
- **APISIX Gateway:** Experiencing restart issues (backend service is healthy)
- **Workaround:** Auth service backend is accessible, gateway issue being resolved

---

## 🎯 How to Use Swagger UI

1. **Open Swagger UI** in your browser using the URLs above
2. **Explore Endpoints** - Click on any endpoint to see details
3. **Try It Out** - Use the "Try it out" button to test APIs
4. **View Schemas** - Check request/response models
5. **Authentication** - Use the "Authorize" button for protected endpoints

---

## 📝 Example API Calls

### Create Domain (Admin Service)
```json
POST http://localhost:8081/api/v1/admin/domains/create_domain/
{
  "name": "example.com",
  "description": "Example domain"
}
```

### User Login (Auth Service)
```json
POST http://localhost:8080/api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Encrypt Data
```json
POST http://localhost:8081/api/v1/admin/encryption/encrypt
{
  "data": "sensitive information"
}
```

---

## 🚀 Getting Started

1. **Access Admin Service Swagger:** http://localhost:8081/docs
2. **Access Auth Service Swagger:** http://localhost:8080/docs
3. **Test APIs** using the interactive documentation
4. **Check service health** using `/health` endpoints
5. **Monitor services** via management interfaces

The Swagger documentation provides complete API specifications, request/response examples, and interactive testing capabilities for both services! 🎉
