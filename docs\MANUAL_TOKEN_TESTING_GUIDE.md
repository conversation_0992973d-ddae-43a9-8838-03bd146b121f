# 🔐 Manual Token Storage Testing Guide

## 🎯 **FIXED: Token Storage Issue Resolved!**

I've identified and fixed the async/sync mixing issue in the token saving function. The tokens should now be properly stored in the PostgreSQL database.

## 🧪 **Step-by-Step Manual Testing**

### **Step 1: Open Required Interfaces**

1. **Swagger UI**: http://localhost:8002/docs (for API testing)
2. **pgAdmin**: http://localhost:5050 (for database verification)
   - Login: `<EMAIL>` / `admin`

### **Step 2: Check Initial Database State**

**In pgAdmin:**
1. Navigate to: **Servers** → **PostgreSQL 13** → **Databases** → **auth_db** → **Schemas** → **public** → **Tables** → **users**
2. Right-click on **users** table → **View/Edit Data** → **All Rows**
3. Note the current users and their `login_token` values (should be NULL initially)

### **Step 3: Register a New User**

**In Swagger UI:**
1. Find `POST /api/v1/users/` endpoint
2. Click **"Try it out"**
3. Use this test data:
```json
{
  "email": "<EMAIL>",
  "first_name": "Token",
  "last_name": "Test",
  "password": "TestPassword123",
  "confirm_password": "TestPassword123",
  "status": true
}
```
4. Click **"Execute"**
5. Should return **201 Created** with user data

### **Step 4: Verify User in Database**

**In pgAdmin:**
1. **Refresh** the users table view (F5 or refresh button)
2. You should see the new user with:
   - `email`: `<EMAIL>`
   - `login_token`: `NULL` (no token yet)

### **Step 5: Login to Generate Token**

**In Swagger UI:**
1. Find `POST /api/v1/login` endpoint
2. Click **"Try it out"**
3. Enter credentials:
   - **username**: `<EMAIL>` (use email as username)
   - **password**: `TestPassword123`
4. Click **"Execute"**
5. Should return **200 OK** with:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### **Step 6: Verify Token Storage in Database**

**In pgAdmin:**
1. **Refresh** the users table view (F5 or refresh button)
2. Look for the user `<EMAIL>`
3. The `login_token` column should now contain the JWT token!
4. You should see:
   - `login_token`: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (full JWT token)
   - `updated_at`: Updated timestamp

### **Step 7: Use Debug Endpoint (Optional)**

**In Swagger UI:**
1. Find `GET /api/v1/debug/tokens` endpoint
2. Click **"Try it out"** → **"Execute"**
3. This will show all users and their token status:
```json
{
  "total_users": 1,
  "tokens": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "has_token": true,
      "token_length": 245,
      "token_preview": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "updated_at": "2024-06-24T..."
    }
  ]
}
```

### **Step 8: Test Token Usage**

**In Swagger UI:**
1. Copy the `access_token` from the login response
2. Find `GET /api/v1/users/me` endpoint
3. Click **"Try it out"**
4. Click the **🔒 lock icon** (Authorize button)
5. Paste your token in the format: `Bearer YOUR_TOKEN_HERE`
6. Click **"Authorize"** → **"Close"**
7. Click **"Execute"** on the `/users/me` endpoint
8. Should return **200 OK** with your user data

## 🔧 **What Was Fixed**

### **Problem Identified:**
- **Async/Sync Mixing**: The `save_login_token` function was async but using synchronous SQLAlchemy operations
- **Database Session Issues**: Potential transaction problems with mixed async/sync calls

### **Solution Applied:**
1. **Created Synchronous Version**: `save_login_token_sync()` for database operations
2. **Async Wrapper**: `save_login_token()` handles caching asynchronously
3. **Better Error Handling**: Detailed debugging and error tracking
4. **Transaction Safety**: Proper commit/rollback handling

### **Code Changes:**
```python
# BEFORE (problematic)
async def save_login_token(db: Session, user_id: int, token: str) -> bool:
    user = await get_user(db, user_id=user_id)  # Async call in sync context
    # ... rest of sync operations

# AFTER (fixed)
def save_login_token_sync(db: Session, user_id: int, token: str) -> bool:
    user = db.query(User).filter(User.id == user_id).first()  # Pure sync
    # ... sync database operations

async def save_login_token(db: Session, user_id: int, token: str) -> bool:
    result = save_login_token_sync(db, user_id, token)  # Call sync version
    # ... async cache operations
```

## ✅ **Expected Results**

### **Successful Token Storage:**
- ✅ **Registration**: User created with `login_token: NULL`
- ✅ **Login**: Returns 200 OK with JWT token
- ✅ **Database**: `login_token` column populated with JWT
- ✅ **Token Format**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.payload.signature`
- ✅ **Token Length**: ~200-300 characters
- ✅ **Updated Timestamp**: `updated_at` field changes after login

### **Token Structure:**
```
Header:    eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9
Payload:   ***************************************************************
Signature: [cryptographic signature]
```

### **Database Verification:**
```sql
-- Run this query in pgAdmin to see tokens
SELECT 
    id,
    email,
    CASE 
        WHEN login_token IS NULL THEN 'No Token'
        ELSE 'Has Token'
    END as token_status,
    LENGTH(login_token) as token_length,
    updated_at
FROM users
ORDER BY updated_at DESC;
```

## 🚨 **Troubleshooting**

### **If Token Still Not Appearing:**

1. **Check Service Logs:**
   ```bash
   docker logs zionix-be-v1-auth-service-1 --tail 50
   ```
   Look for debug messages starting with "DEBUG save_login_token"

2. **Verify Login Success:**
   - Ensure login returns 200 OK
   - Check that access_token is in response

3. **Refresh pgAdmin:**
   - Press F5 or click refresh button
   - Re-run SQL queries

4. **Check Debug Endpoint:**
   - Use `/api/v1/debug/tokens` to see token status
   - Compare with database view

### **Common Issues:**
- **Cached Data**: pgAdmin might show cached data - refresh view
- **Transaction Issues**: Check logs for database errors
- **Token Format**: Ensure token is proper JWT format

## 🎉 **Success Confirmation**

### **You'll Know It's Working When:**
- ✅ Login returns 200 OK with access_token
- ✅ pgAdmin shows JWT token in login_token column
- ✅ Debug endpoint shows `"has_token": true`
- ✅ Token can be used for protected endpoints
- ✅ updated_at timestamp changes after login

## 🌟 **Next Steps After Verification**

1. **✅ Test Multiple Users** - Register and login multiple users
2. **✅ Test Token Expiration** - Verify token lifecycle
3. **✅ Test Protected Endpoints** - Use tokens for API access
4. **✅ Monitor Performance** - Check Redis caching benefits
5. **✅ Continue Development** - All authentication systems working

---

**🎯 The token storage issue has been fixed! Follow this guide to verify that tokens are now being properly stored in PostgreSQL! 🚀**
