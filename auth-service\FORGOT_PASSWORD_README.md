# Forgot Password Flow - Auth Service

This document describes the implementation of the forgot password flow in the auth-service using FastAPI.

## Overview

The forgot password flow allows users to reset their passwords securely using a One-Time Password (OTP) sent to their email address. The flow consists of three main steps:

1. **Request Password Reset**: User enters their email to request a password reset
2. **Verify OTP**: User receives an OTP via email and verifies it
3. **Reset Password**: User sets a new password using the verified OTP

## Features

- ✅ **Email-based OTP**: 6-digit OTP sent to user's email
- ✅ **Time-limited OTPs**: OTPs expire after 5 minutes
- ✅ **One-time use**: OTPs are invalidated after successful use
- ✅ **Security**: No sensitive information exposed in error messages
- ✅ **Email notifications**: Confirmation emails sent after password reset
- ✅ **Event publishing**: Password change events published to Kafka
- ✅ **Database persistence**: OTPs stored securely in database

## API Endpoints

### 1. Request Password Reset

**POST** `/api/v1/auth/forgot-password`

Request an OTP for password reset.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "If the email exists in our system, you will receive an OTP shortly.",
  "email": "<EMAIL>"
}
```

**Status Codes:**
- `200`: Request processed (regardless of email existence for security)
- `500`: Internal server error

### 2. Verify OTP

**POST** `/api/v1/auth/verify-otp`

Verify the OTP received via email.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp_code": "123456"
}
```

**Response:**
```json
{
  "message": "OTP verified successfully. You can now reset your password.",
  "email": "<EMAIL>",
  "is_valid": true
}
```

**Status Codes:**
- `200`: OTP verified successfully
- `400`: Invalid or expired OTP
- `404`: User not found
- `500`: Internal server error

### 3. Reset Password

**POST** `/api/v1/auth/reset-password`

Reset password using a verified OTP.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp_code": "123456",
  "new_password": "NewSecurePassword123!",
  "confirm_password": "NewSecurePassword123!"
}
```

**Response:**
```json
{
  "message": "Password reset successfully. You can now log in with your new password.",
  "email": "<EMAIL>"
}
```

**Status Codes:**
- `200`: Password reset successfully
- `400`: Invalid OTP, expired OTP, or password validation errors
- `404`: User not found
- `500`: Internal server error

## Database Schema

### OTP Table

```sql
CREATE TABLE otps (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    otp_code VARCHAR(6) NOT NULL,
    purpose VARCHAR(50) NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX ix_otps_user_id_purpose ON otps(user_id, purpose);
CREATE INDEX ix_otps_expires_at ON otps(expires_at);
```

## Configuration

### Environment Variables

Add the following email configuration to your `.env` file:

```env
# Email Configuration (for OTP and notifications)
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM=<EMAIL>
MAIL_PORT=587
MAIL_SERVER=smtp.gmail.com
MAIL_FROM_NAME=Zionix Auth Service
```

### Gmail Setup

For Gmail, you'll need to:
1. Enable 2-factor authentication
2. Generate an App Password
3. Use the App Password as `MAIL_PASSWORD`

## Security Features

1. **Rate Limiting**: Consider implementing rate limiting on OTP requests
2. **Email Obfuscation**: Error messages don't reveal if an email exists
3. **OTP Expiry**: OTPs automatically expire after 5 minutes
4. **One-time Use**: OTPs are marked as used after successful password reset
5. **Password Validation**: Strong password requirements enforced
6. **Event Logging**: All password changes are logged and published as events

## Usage Example

### Using cURL

1. **Request OTP:**
```bash
curl -X POST "http://localhost:8001/api/v1/auth/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

2. **Verify OTP:**
```bash
curl -X POST "http://localhost:8001/api/v1/auth/verify-otp" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "otp_code": "123456"}'
```

3. **Reset Password:**
```bash
curl -X POST "http://localhost:8001/api/v1/auth/reset-password" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otp_code": "123456",
    "new_password": "NewSecurePassword123!",
    "confirm_password": "NewSecurePassword123!"
  }'
```

### Using Python Requests

```python
import requests

base_url = "http://localhost:8001/api/v1/auth"

# 1. Request OTP
response = requests.post(f"{base_url}/forgot-password", json={
    "email": "<EMAIL>"
})
print(response.json())

# 2. Verify OTP (after receiving email)
response = requests.post(f"{base_url}/verify-otp", json={
    "email": "<EMAIL>",
    "otp_code": "123456"
})
print(response.json())

# 3. Reset Password
response = requests.post(f"{base_url}/reset-password", json={
    "email": "<EMAIL>",
    "otp_code": "123456",
    "new_password": "NewSecurePassword123!",
    "confirm_password": "NewSecurePassword123!"
})
print(response.json())
```

## Running the Migration

To create the OTP table, run the database migration:

```bash
cd auth-service
python run_migration.py
```

Or using alembic directly:

```bash
cd auth-service
alembic upgrade head
```

## Testing

### Manual Testing

1. Start the auth service
2. Use the API endpoints with a valid email address
3. Check your email for the OTP
4. Verify the OTP and reset your password

### Integration Testing

The implementation includes proper error handling and logging for debugging and monitoring.

## Monitoring and Logging

- All OTP operations are logged with appropriate log levels
- Failed email sends are logged as errors
- Password change events are published to Kafka for audit trails
- Database operations include proper error handling

## Future Enhancements

1. **Rate Limiting**: Implement rate limiting for OTP requests
2. **SMS OTP**: Add SMS as an alternative OTP delivery method
3. **OTP Cleanup**: Implement automatic cleanup of expired OTPs
4. **Admin Dashboard**: Add admin interface to monitor OTP usage
5. **Multi-language**: Support for multiple languages in email templates

## Troubleshooting

### Common Issues

1. **Email not received**: Check spam folder, verify email configuration
2. **OTP expired**: OTPs expire after 5 minutes, request a new one
3. **Invalid OTP**: Ensure the OTP is entered correctly and hasn't been used
4. **Database errors**: Check database connection and run migrations

### Logs

Check the application logs for detailed error information:
- Email sending failures
- Database connection issues
- OTP validation errors
- Password update failures