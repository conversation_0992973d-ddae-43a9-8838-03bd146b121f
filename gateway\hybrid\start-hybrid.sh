#!/bin/bash

echo "========================================"
echo "ZCare Hybrid Gateway Setup"
echo "========================================"
echo ""
echo "Starting Hybrid Gateway Configuration:"
echo "- APISIX Gateway for Auth Service (Port 8080)"
echo "- Envoy Gateway for Admin Service (Port 8081)"
echo ""

echo "🚀 Starting services..."
docker-compose up -d

echo ""
echo "⏳ Waiting for services to be ready..."
sleep 30

echo ""
echo "========================================"
echo "🎉 Hybrid Gateway Started Successfully!"
echo "========================================"
echo ""
echo "🔗 Gateway URLs:"
echo "   Auth Service (APISIX):  http://localhost:8080"
echo "   Admin Service (Envoy):  http://localhost:8081"
echo ""
echo "📊 Management Interfaces:"
echo "   APISIX Metrics:         http://localhost:9091"
echo "   Envoy Admin:            http://localhost:9901"
echo ""
echo "🛣️ API Routes:"
echo ""
echo "🔐 Auth Service Routes (via APISIX - Port 8080):"
echo "   POST http://localhost:8080/api/v1/auth/login"
echo "   POST http://localhost:8080/api/v1/auth/verify-token"
echo "   GET  http://localhost:8080/api/v1/auth/users/me"
echo "   POST http://localhost:8080/api/v1/auth/users/"
echo "   GET  http://localhost:8080/api/v1/auth/encryption/public-key"
echo "   POST http://localhost:8080/api/v1/auth/encryption/encrypt"
echo "   POST http://localhost:8080/api/v1/auth/encryption/decrypt"
echo ""
echo "🏢 Admin Service Routes (via Envoy - Port 8081):"
echo "   POST http://localhost:8081/api/v1/admin/domains/create_domain/"
echo "   GET  http://localhost:8081/api/v1/admin/domains/get_all_domains/"
echo "   GET  http://localhost:8081/api/v1/admin/domains/get_domain/{id}"
echo "   POST http://localhost:8081/api/v1/admin/applications/create_application/"
echo "   GET  http://localhost:8081/api/v1/admin/applications/get_all_applications/"
echo "   GET  http://localhost:8081/api/v1/admin/encryption/public-key"
echo "   POST http://localhost:8081/api/v1/admin/encryption/encrypt"
echo "   POST http://localhost:8081/api/v1/admin/encryption/decrypt"
echo ""
echo "🔍 Health Checks:"
echo "   Auth Gateway:   http://localhost:8080/health"
echo "   Admin Gateway:  http://localhost:8081/health"
echo ""
echo "🧪 Quick Tests:"
echo "   curl http://localhost:8080/health"
echo "   curl http://localhost:8081/health"
echo "   curl http://localhost:8080/api/v1/auth/encryption/public-key"
echo "   curl http://localhost:8081/api/v1/admin/encryption/public-key"
echo ""
echo "📋 Service Status:"
docker-compose ps
echo ""
echo "✅ Hybrid gateway setup complete!"
echo "   - Auth requests → APISIX (Port 8080)"
echo "   - Admin requests → Envoy (Port 8081)"
echo ""
