version: '3.8'

services:
  # APISIX Gateway for Auth Service
  apisix-auth-gateway:
    image: apache/apisix:3.6.0-debian
    restart: always
    volumes:
      - ./apisix-auth.yaml:/usr/local/apisix/conf/apisix.yaml:ro
    depends_on:
      - etcd
      - auth-service
    ports:
      - "8080:9080"  # Auth service gateway
      - "9091:9091"  # Prometheus metrics
    networks:
      - zcare-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9080/apisix/admin/routes"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    environment:
      - APISIX_STAND_ALONE=true

  # Envoy Gateway for Admin Service  
  envoy-admin-gateway:
    image: envoyproxy/envoy:v1.27-latest
    ports:
      - "8081:8081"  # Admin service gateway
      - "9901:9901"  # Admin interface
    volumes:
      - ./envoy-admin.yaml:/etc/envoy/envoy.yaml:ro
    depends_on:
      - admin-service
    networks:
      - zcare-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9901/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    environment:
      - ENVOY_UID=0

  # ETCD for APISIX
  etcd:
    image: bitnami/etcd:3.4.15
    restart: always
    volumes:
      - etcd_data:/bitnami/etcd
    environment:
      ETCD_ENABLE_V2: "true"
      ALLOW_NONE_AUTHENTICATION: "yes"
      ETCD_ADVERTISE_CLIENT_URLS: "http://0.0.0.0:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
    ports:
      - "2379:2379"
      - "2380:2380"
    networks:
      - zcare-network

  # Auth Service (handled by APISIX)
  auth-service:
    build: ../../auth-service
    environment:
      - POSTGRES_SERVER=postgres-auth
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
    depends_on:
      postgres-auth:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Admin Service (handled by Envoy)
  admin-service:
    build: ../../admin-service
    ports:
      - "8001:8000"  # Expose service port for debugging
    environment:
      - POSTGRES_SERVER=postgres-admin
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - AUTH_SERVICE_URL=http://auth-service:8000
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
    depends_on:
      postgres-admin:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      kafka:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Databases
  postgres-auth:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-auth-data:/var/lib/postgresql/data
    ports:
      - "5435:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d auth_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  postgres-admin:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-admin-data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d admin_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_INIT_LIMIT: 10
      ZOOKEEPER_SYNC_LIMIT: 5
      ZOOKEEPER_MAX_CLIENT_CNXNS: 60
      ALLOW_ANONYMOUS_LOGIN: "yes"
      ZOOKEEPER_SERVER_ID: 1
      ZOOKEEPER_SERVERS: server.1=0.0.0.0:2888:3888
      ZOOKEEPER_4LW_COMMANDS_WHITELIST: srvr,ruok,stat,mntr
    ports:
      - "2181:2181"
      - "2888:2888"
      - "3888:3888"
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_log:/var/lib/zookeeper/log
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    healthcheck:
      test: ["CMD-SHELL", "echo stat | nc localhost 2181 || exit 1"]
      interval: 60s
      timeout: 20s
      retries: 15
      start_period: 120s
    restart: unless-stopped
    networks:
      - zcare-network

  kafka:
    image: confluentinc/cp-kafka:7.0.1
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics --bootstrap-server localhost:9092 --list || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

volumes:
  apisix-auth-config:
  postgres-auth-data:
  postgres-admin-data:
  etcd_data:
  zookeeper_data:
  zookeeper_log:

networks:
  zcare-network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1450
