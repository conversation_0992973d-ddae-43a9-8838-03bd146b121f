# ZCare API Gateway

This directory contains multiple API gateway configurations for the ZCare platform, supporting Kong, NGINX, Traefik, Envoy, and APISIX.

## 🚀 Quick Start

### Using the Gateway Selector Scripts

**Windows:**
```bash
gateway\start-gateway.bat
```

**Linux/macOS:**
```bash
chmod +x gateway/start-gateway.sh
./gateway/start-gateway.sh
```

### Manual Gateway Selection

Choose one of the following gateways to run:

## 📋 Available Gateways

### 1. Kong (Default)
- **Port:** 8000 (Gateway), 8444 (Admin)
- **Features:** Plugin ecosystem, Admin API, Rate limiting
- **Start:** `docker-compose up -d` (from root directory)

### 4. Envoy
- **Port:** 8080 (Gateway), 9901 (Admin)
- **Features:** Advanced routing, Observability, gRPC support
- **Start:** `cd gateway/envoy && docker-compose up -d`

### 5. APISIX
- **Port:** 8080 (Gateway), 9091 (Metrics), 9092 (Control)
- **Features:** Dynamic configuration, Plugin system, Prometheus metrics
- **Start:** `cd gateway/apisix && docker-compose up -d`

## 🔗 Service Routes

All gateways expose the same routes:

### Auth Service Routes
- `/api/v1/auth/*` → Auth Service API endpoints
- `/auth/*` → Direct auth service access

### Admin Service Routes
- `/api/v1/admin/*` → Admin Service API endpoints
- `/admin/*` → Direct admin service access

### Health & Info
- `/health` → Gateway health check
- `/` → Gateway information

## 📊 API Endpoints

### Authentication Endpoints
```
POST /api/v1/auth/login
POST /api/v1/auth/verify-token
GET  /api/v1/auth/users/me
POST /api/v1/auth/users/
GET  /api/v1/auth/encryption/public-key
```

### Admin Endpoints
```
POST /api/v1/admin/domains/create_domain/
GET  /api/v1/admin/domains/get_all_domains/
GET  /api/v1/admin/domains/get_domain/{id}
PUT  /api/v1/admin/domains/update_domain/{id}
DELETE /api/v1/admin/domains/delete_domain/{id}

POST /api/v1/admin/applications/create_application/
GET  /api/v1/admin/applications/get_all_applications/
GET  /api/v1/admin/applications/get_application/{id}

GET  /api/v1/admin/encryption/public-key
POST /api/v1/admin/encryption/encrypt
POST /api/v1/admin/encryption/decrypt
```

## 🛠️ Configuration Details

### Rate Limiting
- **Auth endpoints:** 5 req/s, burst 20
- **Admin endpoints:** 10 req/s, burst 50

### CORS
All gateways are configured with permissive CORS for development:
- Origins: `*`
- Methods: `GET, POST, PUT, DELETE, OPTIONS`
- Headers: Standard + Authorization

### Health Checks
All gateways monitor backend service health:
- **Interval:** 30 seconds
- **Timeout:** 10 seconds
- **Path:** `/health`

## 🔧 Environment Variables

Required environment variables (set in docker-compose files):
```
RSA_PRIVATE_KEY=<your-private-key>
RSA_PUBLIC_KEY=<your-public-key>
```

## 📁 Directory Structure

```
gateway/
├── apisix/
│   ├── conf/
│   │   ├── config.yaml
│   │   └── apisix.yaml
│   └── docker-compose.yml
├── envoy/
│   ├── envoy.yaml
│   └── docker-compose.yml
├── kong/
│   └── kong.yml
├── start-gateway.bat
├── start-gateway.sh
├── stop-gateway.bat
├── stop-gateway.sh
└── README.md
```

## 🚦 Testing the Gateway

### Health Check
```bash
curl http://localhost:8080/health
```

### Gateway Info
```bash
curl http://localhost:8080/
```

### Test Auth Service
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=testpass"
```

### Test Admin Service
```bash
curl http://localhost:8080/api/v1/admin/encryption/public-key
```

## 🔍 Monitoring & Observability

### Gateway-specific Monitoring

**Envoy:** Admin interface at http://localhost:9901
**APISIX:** Prometheus metrics at http://localhost:9091

## 🛑 Stopping Gateways

**Stop all gateways:**
```bash
# Windows
gateway\stop-gateway.bat

# Linux/macOS
./gateway/stop-gateway.sh
```

**Stop specific gateway:**
```bash
cd gateway/<gateway-name>
docker-compose down
```

## 🔧 Troubleshooting

### Common Issues

1. **Port conflicts:** Ensure no other services are using ports 8080, 8081, 9901, etc.
2. **Service not starting:** Check Docker logs: `docker-compose logs <service-name>`
3. **502 Bad Gateway:** Verify backend services are healthy
4. **CORS issues:** Check browser console for specific CORS errors

### Debug Commands

```bash
# Check running containers
docker ps

# View logs
docker-compose logs -f <service-name>

# Check service health
docker-compose ps
```

## 📝 Notes

- All gateways use the same backend services (auth-service, admin-service)
- Database and Kafka services are included in each docker-compose file
- Gateway configurations are optimized for development environments
- For production, additional security and performance tuning is recommended
