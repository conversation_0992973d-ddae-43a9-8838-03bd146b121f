version: '3.8'
routes:
  - id: auth-api-routes
    name: "Auth Service API Routes"
  - id: root-route
    name: "Root Route"
    uri: /
    plugins:
      echo:
        body: '{"message": "ZCare API Gateway - APISIX (Auth Service)", "version": "1.0.0", "services": ["auth-service"], "gateway_type": "apisix", "port": 9080}'    name: "Auth Service API Routes"
version: '3.8'

services:
  # APISIX Gateway for Auth Service
  apisix-gateway:
    image: apache/apisix:3.6.0-debian
    restart: always
    volumes:
#!/usr/bin/env python3
"""
ZCare Hybrid Gateway Health Check Script
Monitors APISIX (Auth) + Envoy (Admin) + All Services
"""

import requests
import json
import time
import sys
from datetime import datetime
import subprocess

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_header():
    print(f"{Colors.CYAN}{Colors.BOLD}")
    print("=" * 70)
    print("🏥 ZCare Hybrid Gateway Health Check")
    print("=" * 70)
    print(f"{Colors.END}")
    print(f"{Colors.WHITE}Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Colors.END}")
    print()

def check_service(url, name, timeout=10):
    """Check if a service is healthy"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"{Colors.GREEN}✅ {name:<25} - Healthy{Colors.END}")
            return True, response.json() if 'application/json' in response.headers.get('content-type', '') else response.text
        else:
            print(f"{Colors.RED}❌ {name:<25} - HTTP {response.status_code}{Colors.END}")
            return False, None
    except requests.exceptions.ConnectionError:
        print(f"{Colors.RED}❌ {name:<25} - Connection refused{Colors.END}")
        return False, None
    except requests.exceptions.Timeout:
        print(f"{Colors.YELLOW}⚠️  {name:<25} - Timeout{Colors.END}")
        return False, None
    except Exception as e:
        print(f"{Colors.RED}❌ {name:<25} - Error: {str(e)}{Colors.END}")
        return False, None

def check_redis():
    """Check Redis health using docker exec"""
    try:
        result = subprocess.run([
            'docker', 'exec', 
            subprocess.check_output(['docker-compose', '-f', 'docker-compose.hybrid.yml', 'ps', '-q', 'redis']).decode().strip(),
            'redis-cli', 'ping'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and 'PONG' in result.stdout:
            print(f"{Colors.GREEN}✅ {'Redis':<25} - Healthy{Colors.END}")
            return True
        else:
            print(f"{Colors.RED}❌ {'Redis':<25} - Not responding{Colors.END}")
            return False
    except Exception as e:
        print(f"{Colors.RED}❌ {'Redis':<25} - Error: {str(e)}{Colors.END}")
        return False

def check_docker_services():
    """Check Docker container status"""
    try:
        result = subprocess.run([
            'docker-compose', '-f', 'docker-compose.hybrid.yml', 'ps', '--format', 'json'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            containers = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    containers.append(json.loads(line))
            
            print(f"\n{Colors.BLUE}{Colors.BOLD}📊 Docker Container Status:{Colors.END}")
            print("-" * 70)
            
            all_healthy = True
            for container in containers:
                name = container.get('Name', 'Unknown')
                state = container.get('State', 'Unknown')
                status = container.get('Status', 'Unknown')
                
                if state == 'running':
                    if 'healthy' in status.lower():
                        print(f"{Colors.GREEN}✅ {name:<25} - {status}{Colors.END}")
                    else:
                        print(f"{Colors.YELLOW}⚠️  {name:<25} - {status}{Colors.END}")
                else:
                    print(f"{Colors.RED}❌ {name:<25} - {state}{Colors.END}")
                    all_healthy = False
            
            return all_healthy
        else:
            print(f"{Colors.RED}❌ Failed to get container status{Colors.END}")
            return False
    except Exception as e:
        print(f"{Colors.RED}❌ Error checking containers: {str(e)}{Colors.END}")
        return False

def test_gateway_routing():
    """Test gateway routing functionality"""
    print(f"\n{Colors.PURPLE}{Colors.BOLD}🔀 Testing Gateway Routing:{Colors.END}")
    print("-" * 70)
    
    # Test APISIX routing
    apisix_tests = [
        ("http://localhost:9080/", "APISIX Root"),
        ("http://localhost:9080/health", "APISIX Health"),
        ("http://localhost:9080/auth/health", "APISIX → Auth Service"),
        ("http://localhost:9080/api/v1/auth/health", "APISIX → Auth API"),
    ]
    
    # Test Envoy routing
    envoy_tests = [
        ("http://localhost:10000/", "Envoy Root"),
        ("http://localhost:10000/health", "Envoy Health"),
        ("http://localhost:10000/admin/health", "Envoy → Admin Service"),
        ("http://localhost:10000/api/v1/admin/health", "Envoy → Admin API"),
    ]
    
    all_routes_working = True
    
    for url, name in apisix_tests + envoy_tests:
        success, _ = check_service(url, name, timeout=5)
        if not success:
            all_routes_working = False
    
    return all_routes_working

def get_service_metrics():
    """Get metrics from services"""
    print(f"\n{Colors.CYAN}{Colors.BOLD}📈 Service Metrics:{Colors.END}")
    print("-" * 70)
    
    # APISIX Metrics
    try:
        response = requests.get("http://localhost:9091/apisix/prometheus/metrics", timeout=5)
        if response.status_code == 200:
            print(f"{Colors.GREEN}✅ APISIX Metrics Available{Colors.END}")
        else:
            print(f"{Colors.YELLOW}⚠️  APISIX Metrics - HTTP {response.status_code}{Colors.END}")
    except:
        print(f"{Colors.RED}❌ APISIX Metrics - Not available{Colors.END}")
    
    # Envoy Admin
    try:
        response = requests.get("http://localhost:9901/stats", timeout=5)
        if response.status_code == 200:
            print(f"{Colors.GREEN}✅ Envoy Admin Interface Available{Colors.END}")
        else:
            print(f"{Colors.YELLOW}⚠️  Envoy Admin - HTTP {response.status_code}{Colors.END}")
    except:
        print(f"{Colors.RED}❌ Envoy Admin - Not available{Colors.END}")
    
    # Redis Stats
    try:
        response = requests.get("http://localhost:8002/health/cache/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"{Colors.GREEN}✅ Redis Cache Stats:{Colors.END}")
            print(f"   - Status: {stats.get('status', 'unknown')}")
            print(f"   - Hits: {stats.get('keyspace_hits', 'N/A')}")
            print(f"   - Misses: {stats.get('keyspace_misses', 'N/A')}")
        else:
            print(f"{Colors.YELLOW}⚠️  Redis Stats - HTTP {response.status_code}{Colors.END}")
    except:
        print(f"{Colors.RED}❌ Redis Stats - Not available{Colors.END}")

def main():
    print_header()
    
    # Core Services Health Check
    print(f"{Colors.BLUE}{Colors.BOLD}🏥 Core Services Health:{Colors.END}")
    print("-" * 70)
    
    services = [
        ("http://localhost:9080/health", "APISIX Gateway"),
        ("http://localhost:10000/health", "Envoy Gateway"),
        ("http://localhost:8002/health", "Auth Service"),
        ("http://localhost:8001/health", "Admin Service"),
        ("http://localhost:5050/misc/ping", "pgAdmin"),
    ]
    
    healthy_services = 0
    total_services = len(services) + 1  # +1 for Redis
    
    for url, name in services:
        success, _ = check_service(url, name)
        if success:
            healthy_services += 1
    
    # Check Redis separately
    if check_redis():
        healthy_services += 1
    
    # Check Docker containers
    containers_healthy = check_docker_services()
    
    # Test gateway routing
    routing_working = test_gateway_routing()
    
    # Get service metrics
    get_service_metrics()
    
    # Summary
    print(f"\n{Colors.BOLD}📋 Health Summary:{Colors.END}")
    print("-" * 70)
    print(f"Services Healthy: {healthy_services}/{total_services}")
    print(f"Containers Status: {'✅ All Running' if containers_healthy else '❌ Issues Detected'}")
    print(f"Gateway Routing: {'✅ Working' if routing_working else '❌ Issues Detected'}")
    
    if healthy_services == total_services and containers_healthy and routing_working:
        print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 All systems are healthy and operational!{Colors.END}")
        
        print(f"\n{Colors.WHITE}{Colors.BOLD}🌐 Quick Access URLs:{Colors.END}")
        print(f"   - APISIX Gateway:     http://localhost:9080")
        print(f"   - Envoy Gateway:      http://localhost:10000")
        print(f"   - Auth Service:       http://localhost:8002")
        print(f"   - Admin Service:      http://localhost:8001")
        print(f"   - pgAdmin:            http://localhost:5050")
        
        return 0
    else:
        print(f"\n{Colors.RED}{Colors.BOLD}⚠️  Some issues detected. Check logs:{Colors.END}")
        print(f"   docker-compose -f docker-compose.hybrid.yml logs")
        return 1

if __name__ == "__main__":
    sys.exit(main())    volumes:
@echo off
REM ZCare Hybrid Gateway Services Startup Script
REM APISIX for Auth Service + Envoy for Admin Service

echo 🚀 Starting ZCare Hybrid Gateway Services...
echo 📋 Architecture:
echo    - APISIX Gateway (port 9080) → Auth Service
echo    - Envoy Gateway (port 10000) → Admin Service
echo    - Redis Cache (port 6379)
echo    - PostgreSQL Databases (ports 5433, 5434)
echo    - pgAdmin (port 5050)
echo    - Kafka + Zookeeper (ports 9092, 2181)
echo    - Prometheus (port 9090) + Grafana (port 3000)
echo.

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose not found. Please install docker-compose.
    pause
    exit /b 1
)

REM Stop any existing services
echo 🛑 Stopping existing services...
docker-compose -f docker-compose.hybrid.yml down --remove-orphans

REM Clean up
echo 🧹 Cleaning up...
docker system prune -f --volumes

REM Start services
echo 🔄 Starting hybrid gateway services...
docker-compose -f docker-compose.hybrid.yml up -d --build

REM Wait for services to start
echo ⏳ Waiting for services to initialize (60 seconds)...
timeout /t 60 /nobreak >nul

REM Check service health
echo 🔍 Checking service health...

curl -f -s http://localhost:9080/health >nul 2>&1
if errorlevel 1 (
    echo ❌ APISIX Gateway is not responding
) else (
    echo ✅ APISIX Gateway is healthy
)

curl -f -s http://localhost:10000/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Envoy Gateway is not responding
) else (
    echo ✅ Envoy Gateway is healthy
)

curl -f -s http://localhost:8002/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Auth Service is not responding
) else (
    echo ✅ Auth Service is healthy
)

curl -f -s http://localhost:8001/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Admin Service is not responding
) else (
    echo ✅ Admin Service is healthy
)

echo.
echo 📊 Service Status:
docker-compose -f docker-compose.hybrid.yml ps

echo.
echo 🎉 Hybrid Gateway Services Started!
echo.
echo 🌐 Access Points:
echo    - APISIX Gateway (Auth):    http://localhost:9080
echo    - Envoy Gateway (Admin):    http://localhost:10000
echo    - Auth Service Direct:      http://localhost:8002
echo    - Admin Service Direct:     http://localhost:8001
echo    - pgAdmin:                  http://localhost:5050
echo    - Prometheus:               http://localhost:9090
echo    - Grafana:                  http://localhost:3000
echo.
echo 📚 API Documentation:
echo    - Auth Service Swagger:     http://localhost:8002/docs
echo    - Admin Service Swagger:    http://localhost:8001/docs
echo.
echo 🔧 Gateway Admin Interfaces:
echo    - APISIX Control API:       http://localhost:9092
echo    - Envoy Admin Interface:    http://localhost:9901
echo.
echo 🧪 Test Commands:
echo    # Test APISIX (Auth Service)
echo    curl http://localhost:9080/auth/health
echo    curl http://localhost:9080/api/v1/auth/health
echo.
echo    # Test Envoy (Admin Service)
echo    curl http://localhost:10000/admin/health
echo    curl http://localhost:10000/api/v1/admin/health
echo.
echo 📋 Credentials:
echo    - pgAdmin: <EMAIL> / admin123
echo    - Database: postgres / Arunnathan
echo.
pause    volumes:
#!/bin/bash

# ZCare Hybrid Gateway Services Startup Script
# APISIX for Auth Service + Envoy for Admin Service

set -e

echo "🚀 Starting ZCare Hybrid Gateway Services..."
echo "📋 Architecture:"
echo "   - APISIX Gateway (port 9080) → Auth Service"
echo "   - Envoy Gateway (port 10000) → Admin Service"
echo "   - Redis Cache (port 6379)"
echo "   - PostgreSQL Databases (ports 5433, 5434)"
echo "   - pgAdmin (port 5050)"
echo "   - Kafka + Zookeeper (ports 9092, 2181)"
echo "   - Prometheus (port 9090) + Grafana (port 3000)"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found. Please install docker-compose."
    exit 1
fi

# Stop any existing services
echo "🛑 Stopping existing services..."
docker-compose -f docker-compose.hybrid.yml down --remove-orphans

# Clean up any orphaned containers
echo "🧹 Cleaning up..."
docker system prune -f --volumes

# Start services
echo "🔄 Starting hybrid gateway services..."
docker-compose -f docker-compose.hybrid.yml up -d --build

# Wait for services to start
echo "⏳ Waiting for services to initialize (60 seconds)..."
sleep 60

# Check service health
echo "🔍 Checking service health..."

services=(
    "http://localhost:9080/health:APISIX Gateway"
    "http://localhost:10000/health:Envoy Gateway"
    "http://localhost:8002/health:Auth Service"
    "http://localhost:8001/health:Admin Service"
    "http://localhost:6379:Redis"
)

all_healthy=true

for service in "${services[@]}"; do
    url=$(echo $service | cut -d: -f1-2)
    name=$(echo $service | cut -d: -f3)
    
    if [[ $name == "Redis" ]]; then
        if docker exec $(docker-compose -f docker-compose.hybrid.yml ps -q redis) redis-cli ping > /dev/null 2>&1; then
            echo "✅ $name is healthy"
        else
            echo "❌ $name is not responding"
            all_healthy=false
        fi
    else
        if curl -f -s $url > /dev/null 2>&1; then
            echo "✅ $name is healthy"
        else
            echo "❌ $name is not responding"
            all_healthy=false
        fi
    fi
done

echo ""
echo "📊 Service Status:"
docker-compose -f docker-compose.hybrid.yml ps

if $all_healthy; then
    echo ""
    echo "🎉 All services are running successfully!"
    echo ""
    echo "🌐 Access Points:"
    echo "   - APISIX Gateway (Auth):    http://localhost:9080"
    echo "   - Envoy Gateway (Admin):    http://localhost:10000"
    echo "   - Auth Service Direct:      http://localhost:8002"
    echo "   - Admin Service Direct:     http://localhost:8001"
    echo "   - pgAdmin:                  http://localhost:5050"
    echo "   - Prometheus:               http://localhost:9090"
    echo "   - Grafana:                  http://localhost:3000"
    echo ""
    echo "📚 API Documentation:"
    echo "   - Auth Service Swagger:     http://localhost:8002/docs"
    echo "   - Admin Service Swagger:    http://localhost:8001/docs"
    echo ""
    echo "🔧 Gateway Admin Interfaces:"
    echo "   - APISIX Control API:       http://localhost:9092"
    echo "   - Envoy Admin Interface:    http://localhost:9901"
    echo ""
    echo "🧪 Test Commands:"
    echo "   # Test APISIX (Auth Service)"
    echo "   curl http://localhost:9080/auth/health"
    echo "   curl http://localhost:9080/api/v1/auth/health"
    echo ""
    echo "   # Test Envoy (Admin Service)"
    echo "   curl http://localhost:10000/admin/health"
    echo "   curl http://localhost:10000/api/v1/admin/health"
    echo ""
    echo "📋 Credentials:"
    echo "   - pgAdmin: <EMAIL> / admin123"
    echo "   - Database: postgres / Arunnathan"
else
    echo ""
    echo "⚠️  Some services are not healthy. Check logs:"
    echo "   docker-compose -f docker-compose.hybrid.yml logs"
    exit 1
fi    volumes:
      - ./gateway/apisix/conf/config.yaml:/usr/local/apisix/conf/config.yaml:ro
      - ./gateway/apisix/conf/apisix.yaml:/usr/local/apisix/conf/apisix.yaml:ro
    depends_on:
      - etcd
      - auth-service
    ports:
      - "9080:9080"  # APISIX Gateway
      - "9091:9091"  # Prometheus metrics
      - "9092:9092"  # Control API
    networks:
      - zcare-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    environment:
      - APISIX_STAND_ALONE=true

  # Envoy Gateway for Admin Service
  envoy-gateway:
    image: envoyproxy/envoy:v1.27-latest
    ports:
      - "10000:10000"  # Envoy Gateway
      - "9901:9901"    # Admin interface
    volumes:
      - ./gateway/envoy/envoy.yaml:/etc/envoy/envoy.yaml:ro
    depends_on:
      - admin-service
    networks:
      - zcare-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9901/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # etcd for APISIX
  etcd:
    image: bitnami/etcd:3.4.15
    restart: always
    volumes:
      - etcd_data:/bitnami/etcd
    environment:
      ETCD_ENABLE_V2: "true"
      ALLOW_NONE_AUTHENTICATION: "yes"
      ETCD_ADVERTISE_CLIENT_URLS: "http://0.0.0.0:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
    ports:
      - "2379:2379"
      - "2380:2380"
    networks:
      - zcare-network

  # Admin Service
  admin-service:
    build: ./admin-service
    ports:
      - "8001:8000"
    environment:
      - POSTGRES_SERVER=postgres-admin
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - AUTH_SERVICE_URL=http://auth-service:8000
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
    depends_on:
      postgres-admin:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      kafka:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Auth Service
  auth-service:
    build: ./auth-service
    ports:
      - "8002:8000"
    environment:
      - POSTGRES_SERVER=postgres-auth
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - ENABLE_CACHE=true
      - CACHE_TTL=300
    depends_on:
      postgres-auth:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # Databases
  postgres-admin:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-admin-data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d admin_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  postgres-auth:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-auth-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d auth_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    depends_on:
      - postgres-admin
      - postgres-auth
    restart: unless-stopped
    networks:
      - zcare-network

  # Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["CMD", "bash", "-c", "echo 'ruok' | nc localhost 2181"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  kafka:
    image: confluentinc/cp-kafka:7.0.1
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics --bootstrap-server localhost:9092 --list"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # Observability
  prometheus:
    image: prom/prometheus:v2.30.3
    volumes:
      - ./observability/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - zcare-network

  grafana:
    image: grafana/grafana:8.2.2
    volumes:
      - ./observability/grafana/provisioning:/etc/grafana/provisioning
      - ./observability/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - zcare-network

volumes:
  postgres-admin-data:
  postgres-auth-data:
  redis-data:
  pgadmin-data:
  etcd_data:

networks:
  zcare-network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1450        body: '{"message": "ZCare API Gateway - APISIX (Auth Service)", "version": "1.0.0", "services": ["auth-service"], "gateway_type": "apisix", "port": 9080}'    name: "Auth Service API Routes"
                address: admin-service
                port_value: 8000
    circuit_breakers:
      thresholds:
      - priority: DEFAULT
        max_connections: 100
        max_pending_requests: 50
        max_requests: 200
        max_retries: 3        body: '{"message": "ZCare API Gateway - APISIX (Auth Service)", "version": "1.0.0", "services": ["auth-service"], "gateway_type": "apisix", "port": 9080}'    name: "Auth Service API Routes"
      clusters:
      - name: admin-service        body: '{"message": "ZCare API Gateway - APISIX (Auth Service)", "version": "1.0.0", "services": ["auth-service"], "gateway_type": "apisix", "port": 9080}'    name: "Auth Service API Routes"
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 10000        body: '{"message": "ZCare API Gateway - APISIX (Auth Service)", "version": "1.0.0", "services": ["auth-service"], "gateway_type": "apisix", "port": 9080}'    name: "Auth Service API Routes"
                  body:
                    inline_string: '{"message": "ZCare API Gateway - Envoy (Admin Service)", "version": "1.0.0", "services": ["admin-service"], "gateway_type": "envoy", "port": 10000}'        body: '{"message": "ZCare API Gateway - APISIX (Auth Service)", "version": "1.0.0", "services": ["auth-service"], "gateway_type": "apisix", "port": 9080}'    name: "Auth Service API Routes"
upstreams:
  - id: auth-service
    name: "Auth Service Upstream"
    type: roundrobin
    nodes:
      "auth-service:8000": 1
    checks:
      active:
        type: http
        http_path: "/health"
        healthy:
          interval: 30
          http_statuses: [200, 302]
          successes: 2
        unhealthy:
          interval: 30
          http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
          http_failures: 3
          timeouts: 3
    timeout:
      connect: 6
      send: 6
      read: 6    name: "Auth Service API Routes"
    uri: /api/v1/auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
      checks:
        active:
          type: http
          http_path: "/health"
          healthy:
            interval: 30
            http_statuses: [200, 302]
            successes: 2
          unhealthy:
            interval: 30
            http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
            http_failures: 3
            timeouts: 3
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      limit-req:
        rate: 10
        burst: 30
        rejected_code: 429
        nodelay: true
      proxy-cache:
        cache_zone: "auth_cache"
        cache_key: ["$host", "$request_uri"]
        cache_bypass: ["$arg_nocache"]
        cache_method: ["GET", "HEAD"]
        cache_http_status: [200, 301, 404]
        hide_cache_headers: true
        cache_ttl: 300
      proxy-rewrite:
        regex_uri: ["^/api/v1/auth/(.*)", "/api/v1/$1"]
      prometheus:
        prefer_name: true

  - id: auth-direct-routes
    name: "Auth Service Direct Routes"
    uri: /auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
      checks:
        active:
          type: http
          http_path: "/health"
          healthy:
            interval: 30
            http_statuses: [200, 302]
            successes: 2
          unhealthy:
            interval: 30
            http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
            http_failures: 3
            timeouts: 3
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      limit-req:
        rate: 10
        burst: 30
        rejected_code: 429
        nodelay: true
      proxy-cache:
        cache_zone: "auth_cache"
        cache_key: ["$host", "$request_uri"]
        cache_bypass: ["$arg_nocache"]
        cache_method: ["GET", "HEAD"]
        cache_http_status: [200, 301, 404]
        hide_cache_headers: true
        cache_ttl: 300
      proxy-rewrite:
        regex_uri: ["^/auth/(.*)", "/$1"]
      prometheus:
        prefer_name: true
                  body:
                    inline_string: '{"message": "ZCare API Gateway - Envoy (Admin Service)", "version": "1.0.0", "services": ["admin-service"], "gateway_type": "envoy", "port": 10000}'
services:
  # APISIX Gateway for Auth Service
  apisix-gateway:
    image: apache/apisix:3.6.0-debian
    restart: always
    volumes:
      - ./gateway/apisix/conf/config.yaml:/usr/local/apisix/conf/config.yaml:ro
      - ./gateway/apisix/conf/apisix.yaml:/usr/local/apisix/conf/apisix.yaml:ro
    depends_on:
      - etcd
      - auth-service
    ports:
      - "9080:9080"  # APISIX Gateway
      - "9091:9091"  # Prometheus metrics
      - "9092:9092"  # Control API
    networks:
      - zcare-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    environment:
      - APISIX_STAND_ALONE=true

  # Envoy Gateway for Admin Service
  envoy-gateway:
    image: envoyproxy/envoy:v1.27-latest
    ports:
      - "10000:10000"  # Envoy Gateway
      - "9901:9901"    # Admin interface
    volumes:
      - ./gateway/envoy/envoy.yaml:/etc/envoy/envoy.yaml:ro
    depends_on:
      - admin-service
    networks:
      - zcare-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9901/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # etcd for APISIX
  etcd:
    image: bitnami/etcd:3.4.15
    restart: always
    volumes:
      - etcd_data:/bitnami/etcd
    environment:
      ETCD_ENABLE_V2: "true"
      ALLOW_NONE_AUTHENTICATION: "yes"
      ETCD_ADVERTISE_CLIENT_URLS: "http://0.0.0.0:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
    ports:
      - "2379:2379"
      - "2380:2380"
    networks:
      - zcare-network

  # Admin Service
  admin-service:
    build: ./admin-service
    ports:
      - "8001:8000"
    environment:
      - POSTGRES_SERVER=postgres-admin
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - AUTH_SERVICE_URL=http://auth-service:8000
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
    depends_on:
      postgres-admin:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      kafka:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Auth Service
  auth-service:
    build: ./auth-service
    ports:
      - "8002:8000"
    environment:
      - POSTGRES_SERVER=postgres-auth
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - ENABLE_CACHE=true
      - CACHE_TTL=300
    depends_on:
      postgres-auth:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # Databases
  postgres-admin:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-admin-data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d admin_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  postgres-auth:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-auth-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d auth_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    depends_on:
      - postgres-admin
      - postgres-auth
    restart: unless-stopped
    networks:
      - zcare-network

  # Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["CMD", "bash", "-c", "echo 'ruok' | nc localhost 2181"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  kafka:
    image: confluentinc/cp-kafka:7.0.1
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics --bootstrap-server localhost:9092 --list"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # Observability
  prometheus:
    image: prom/prometheus:v2.30.3
    volumes:
      - ./observability/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - zcare-network

  grafana:
    image: grafana/grafana:8.2.2
    volumes:
      - ./observability/grafana/provisioning:/etc/grafana/provisioning
      - ./observability/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - zcare-network

volumes:
  postgres-admin-data:
  postgres-auth-data:
  redis-data:
  pgadmin-data:
  etcd_data:

networks:
  zcare-network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1450routes:
  - id: auth-api-routes
    name: "Auth Service API Routes"
    uri: /api/v1/auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
      checks:
        active:
          type: http
          http_path: "/health"
          healthy:
            interval: 30
            http_statuses: [200, 302]
            successes: 2
          unhealthy:
            interval: 30
            http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
            http_failures: 3
            timeouts: 3
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      limit-req:
        rate: 10
        burst: 30
        rejected_code: 429
        nodelay: true
      proxy-cache:
        cache_zone: "auth_cache"
        cache_key: ["$host", "$request_uri"]
        cache_bypass: ["$arg_nocache"]
        cache_method: ["GET", "HEAD"]
        cache_http_status: [200, 301, 404]
        hide_cache_headers: true
        cache_ttl: 300
      proxy-rewrite:
        regex_uri: ["^/api/v1/auth/(.*)", "/api/v1/$1"]
      prometheus:
        prefer_name: true

  - id: auth-direct-routes
    name: "Auth Service Direct Routes"
    uri: /auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
      checks:
        active:
          type: http
          http_path: "/health"
          healthy:
            interval: 30
            http_statuses: [200, 302]
            successes: 2
          unhealthy:
            interval: 30
            http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
            http_failures: 3
            timeouts: 3
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      limit-req:
        rate: 10
        burst: 30
        rejected_code: 429
        nodelay: true
      proxy-cache:
        cache_zone: "auth_cache"
        cache_key: ["$host", "$request_uri"]
        cache_bypass: ["$arg_nocache"]
        cache_method: ["GET", "HEAD"]
        cache_http_status: [200, 301, 404]
        hide_cache_headers: true
        cache_ttl: 300
      proxy-rewrite:
        regex_uri: ["^/auth/(.*)", "/$1"]
      prometheus:
        prefer_name: true
services:
  apisix-gateway:
    image: apache/apisix:3.6.0-debian
    restart: always
    volumes:
      - ./conf/config.yaml:/usr/local/apisix/conf/config.yaml:ro
      - ./conf/apisix.yaml:/usr/local/apisix/conf/apisix.yaml:ro
    depends_on:
      - etcd
      - admin-service
      - auth-service
    ports:
      - "8080:9080"
      - "9091:9091"  # Prometheus metrics
      - "9092:9092"  # Control API
    networks:
      - zcare-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  etcd:
    image: bitnami/etcd:3.4.15
    restart: always
    volumes:
      - etcd_data:/bitnami/etcd
    environment:
      ETCD_ENABLE_V2: "true"
      ALLOW_NONE_AUTHENTICATION: "yes"
      ETCD_ADVERTISE_CLIENT_URLS: "http://0.0.0.0:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
    ports:
      - "2379:2379"
      - "2380:2380"
    networks:
      - zcare-network

  # Admin Service
  admin-service:
    build: ../../admin-service
    environment:
      - POSTGRES_SERVER=postgres-admin
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - AUTH_SERVICE_URL=http://auth-service:8000
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
    depends_on:
      postgres-admin:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      kafka:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Auth Service
  auth-service:
    build: ../../auth-service
    environment:
      - POSTGRES_SERVER=postgres-auth
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
    depends_on:
      postgres-auth:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Databases
  postgres-admin:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-admin-data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d admin_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  postgres-auth:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-auth-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d auth_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["CMD", "bash", "-c", "echo 'ruok' | nc localhost 2181"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  kafka:
    image: confluentinc/cp-kafka:7.0.1
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics --bootstrap-server localhost:9092 --list"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

volumes:
  postgres-admin-data:
  postgres-auth-data:
  etcd_data:

networks:
  zcare-network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450
      com.docker.network.driver.mtu: 1450