# 📁 File Organization Architecture

This document describes the organized file structure for the ZCare microservices platform, with dedicated directories for different file types.

## 🏗️ Directory Structure

```
zionix-be-v1/
├── keys/                           # 🔐 Encryption Keys
│   ├── README.md
│   ├── private.pem                 # RSA private key (2048-bit)
│   └── public.pem                  # RSA public key (2048-bit)
├── scripts/
│   ├── windows/                    # 🪟 Windows Scripts (.bat)
│   │   ├── README.md
│   │   ├── start-all-services.bat
│   │   ├── start-hybrid-services.bat
│   │   └── docker-build-with-retry.bat
│   ├── create-microservice.sh      # 🐧 Unix Scripts (.sh)
│   ├── deploy.sh
│   ├── docker-build-with-retry.sh
│   └── rename-project.sh
├── auth-service/                   # 🔐 Authentication Service
├── admin-service/                  # 👨‍💼 Administration Service
├── gateway/                        # 🌐 API Gateways
├── docs/                          # 📚 Documentation
├── k8s/                           # ☸️ Kubernetes Manifests
├── observability/                 # 📊 Monitoring Stack
└── templates/                     # 📋 Project Templates
```

## 🔐 Keys Directory (`/keys`)

### Purpose
Centralized storage for RSA encryption keys used across all microservices.

### Files
- **`private.pem`** - RSA private key for decryption operations
- **`public.pem`** - RSA public key for encryption operations
- **`README.md`** - Documentation for key management

### Security Features
- Isolated from application code
- Proper Docker volume mounting
- Environment variable fallback support
- Clear documentation for key rotation

### Usage in Services
```yaml
# Docker Compose Volume Mounting
volumes:
  - ./keys/private.pem:/app/keys/private.pem
  - ./keys/public.pem:/app/keys/public.pem
```

## 🪟 Windows Scripts Directory (`/scripts/windows`)

### Purpose
Windows-specific batch scripts for development and deployment operations.

### Files
- **`start-all-services.bat`** - Start complete microservices stack
- **`start-hybrid-services.bat`** - Start hybrid gateway architecture
- **`docker-build-with-retry.bat`** - Docker build with retry logic

### Cross-Platform Support
- Windows: `.bat` files in `/scripts/windows/`
- Unix/Linux: `.sh` files in `/scripts/`
- Consistent functionality across platforms

## 🔧 Configuration Updates

### Auth Service
**File**: `auth-service/app/core/config.py`
```python
# Updated key paths
VaultConfig.PRIVATE_KEY = VaultConfig._read_key_file("keys/private.pem")
VaultConfig.PUBLIC_KEY = VaultConfig._read_key_file("keys/public.pem")
```

### Admin Service
**File**: `admin-service/app/core/hybrid_encryption.py`
```python
# Fallback to file-based key loading
try:
    with open("keys/private.pem", "r") as f:
        private_key_pem = f.read()
    with open("keys/public.pem", "r") as f:
        public_key_pem = f.read()
except FileNotFoundError:
    # Generate new keys if files don't exist
    self._generate_rsa_keys()
```

### Docker Compose
**File**: `docker-compose.hybrid.yml`
```yaml
# Both services now mount keys from centralized location
volumes:
  - ./keys/private.pem:/app/keys/private.pem
  - ./keys/public.pem:/app/keys/public.pem
```

## 📋 Benefits of This Architecture

### 🔒 Security
- **Centralized Key Management**: All encryption keys in one secure location
- **Clear Separation**: Keys isolated from application code
- **Proper Permissions**: Dedicated directory for security controls

### 🛠️ Maintainability
- **Platform-Specific Scripts**: Windows and Unix scripts properly separated
- **Clear Documentation**: Each directory has comprehensive README
- **Consistent Structure**: Predictable file locations

### 🚀 Deployment
- **Docker Integration**: Proper volume mounting for keys
- **Environment Flexibility**: Support for both file and environment variable keys
- **Cross-Platform**: Scripts work on Windows and Unix systems

### 🔄 Development Workflow
- **Easy Key Rotation**: Centralized location for key updates
- **Script Organization**: Platform-specific automation tools
- **Clear Dependencies**: Explicit file relationships

## 🎯 Usage Examples

### Starting Services (Windows)
```cmd
# Start all services
scripts\windows\start-all-services.bat

# Start hybrid architecture
scripts\windows\start-hybrid-services.bat
```

### Starting Services (Unix/Linux)
```bash
# Start hybrid architecture
./start-hybrid-services.sh

# Build with retry
scripts/docker-build-with-retry.sh
```

### Key Management
```bash
# Navigate to keys directory
cd keys

# Generate new keys
openssl genrsa -out private.pem 2048
openssl rsa -in private.pem -pubout -out public.pem

# Set proper permissions
chmod 600 private.pem
chmod 644 public.pem
```

## 🔍 Migration Notes

### What Changed
1. **Keys moved**: `private.pem`, `public.pem` → `keys/`
2. **Scripts organized**: `.bat` files → `scripts/windows/`
3. **Configuration updated**: File paths updated in services
4. **Docker volumes**: Updated mount paths

### Backward Compatibility
- Environment variables still supported for keys
- Fallback mechanisms in place
- Gradual migration approach

This organized structure provides better security, maintainability, and cross-platform support for the ZCare microservices platform.
