from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional
from datetime import datetime
import re
from app.regex.regex_patterns import AUTH_PASSWORD_PATTERN

class ForgotPasswordRequest(BaseModel):
    email: EmailStr

class VerifyOTPRequest(BaseModel):
    email: EmailStr
    otp_code: str = Field(..., min_length=6, max_length=6, pattern=r"^\d{6}$")

class ResetPasswordRequest(BaseModel):
    email: EmailStr
    otp_code: str = Field(..., min_length=6, max_length=6, pattern=r"^\d{6}$")
    new_password: str = Field(..., pattern=AUTH_PASSWORD_PATTERN)
    confirm_password: str = Field(..., pattern=AUTH_PASSWORD_PATTERN)

    @field_validator('new_password', 'confirm_password')
    @classmethod
    def validate_password_complexity(cls, v):
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not re.search(r"[a-z]", v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not re.search(r"[A-Z]", v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not re.search(r"\d", v):
            raise ValueError("Password must contain at least one number")
        if not re.search(r"[@$!%*?&]", v):
            raise ValueError("Password must contain at least one special character (@$!%*?&)")
        return v

    def validate_passwords_match(self):
        if self.new_password != self.confirm_password:
            raise ValueError("Passwords do not match")
        return self

class ForgotPasswordResponse(BaseModel):
    message: str
    email: str

class VerifyOTPResponse(BaseModel):
    message: str
    email: str
    is_valid: bool

class ResetPasswordResponse(BaseModel):
    message: str
    email: str

class OTPResponse(BaseModel):
    id: int
    user_id: int
    purpose: str
    is_used: bool
    expires_at: datetime
    created_at: datetime

    model_config = {"from_attributes": True}