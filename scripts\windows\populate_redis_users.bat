@echo off
echo Populating Redis with realistic user data...
echo ==========================================

REM Clear existing user data
echo Clearing existing user data...
docker exec zionix-be-v1-redis-1 redis-cli DEL user:1
docker exec zionix-be-v1-redis-1 redis-cli DEL user:2
docker exec zionix-be-v1-redis-1 redis-cli DEL user:email:<EMAIL>
docker exec zionix-be-v1-redis-1 redis-cli DEL user:email:<EMAIL>
docker exec zionix-be-v1-redis-1 redis-cli DEL login_token:1
docker exec zionix-be-v1-redis-1 redis-cli DEL login_token:2

echo.
echo Adding realistic user data...

REM User 1 - <PERSON> (Regular User)
echo Adding John Doe...
docker exec zionix-be-v1-redis-1 redis-cli SETEX user:1 600 "{\"id\":1,\"email\":\"<EMAIL>\",\"first_name\":\"John\",\"last_name\":\"Doe\",\"hashed_password\":\"$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u\",\"status\":true,\"action\":\"user\",\"login_token\":null,\"created_at\":\"2024-01-17T10:00:00.000Z\",\"updated_at\":null}"

docker exec zionix-be-v1-redis-1 redis-cli SETEX user:email:<EMAIL> 600 "{\"id\":1,\"email\":\"<EMAIL>\",\"first_name\":\"John\",\"last_name\":\"Doe\",\"hashed_password\":\"$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u\",\"status\":true,\"action\":\"user\",\"login_token\":null,\"created_at\":\"2024-01-17T10:00:00.000Z\",\"updated_at\":null}"

REM User 2 - Jane Smith (Admin User)
echo Adding Jane Smith...
docker exec zionix-be-v1-redis-1 redis-cli SETEX user:2 600 "{\"id\":2,\"email\":\"<EMAIL>\",\"first_name\":\"Jane\",\"last_name\":\"Smith\",\"hashed_password\":\"$2b$12$8k2ydShrf4IfH0.tlB0F4e4xtTGdMn8jmjbGbXD4/g6LrjSJxFm.K\",\"status\":true,\"action\":\"admin\",\"login_token\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.sample_token\",\"created_at\":\"2024-01-17T09:30:00.000Z\",\"updated_at\":\"2024-01-17T10:15:00.000Z\"}"

docker exec zionix-be-v1-redis-1 redis-cli SETEX user:email:<EMAIL> 600 "{\"id\":2,\"email\":\"<EMAIL>\",\"first_name\":\"Jane\",\"last_name\":\"Smith\",\"hashed_password\":\"$2b$12$8k2ydShrf4IfH0.tlB0F4e4xtTGdMn8jmjbGbXD4/g6LrjSJxFm.K\",\"status\":true,\"action\":\"admin\",\"login_token\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.sample_token\",\"created_at\":\"2024-01-17T09:30:00.000Z\",\"updated_at\":\"2024-01-17T10:15:00.000Z\"}"

REM User 3 - Mike Johnson (Inactive User)
echo Adding Mike Johnson...
docker exec zionix-be-v1-redis-1 redis-cli SETEX user:3 600 "{\"id\":3,\"email\":\"<EMAIL>\",\"first_name\":\"Mike\",\"last_name\":\"Johnson\",\"hashed_password\":\"$2b$12$9m3ydShrf4IfH0.tlB0F4e4xtTGdMn8jmjbGbXD4/g6LrjSJxFm.L\",\"status\":false,\"action\":\"user\",\"login_token\":null,\"created_at\":\"2024-01-16T14:20:00.000Z\",\"updated_at\":\"2024-01-17T08:45:00.000Z\"}"

docker exec zionix-be-v1-redis-1 redis-cli SETEX user:email:<EMAIL> 600 "{\"id\":3,\"email\":\"<EMAIL>\",\"first_name\":\"Mike\",\"last_name\":\"Johnson\",\"hashed_password\":\"$2b$12$9m3ydShrf4IfH0.tlB0F4e4xtTGdMn8jmjbGbXD4/g6LrjSJxFm.L\",\"status\":false,\"action\":\"user\",\"login_token\":null,\"created_at\":\"2024-01-16T14:20:00.000Z\",\"updated_at\":\"2024-01-17T08:45:00.000Z\"}"

REM Login tokens
echo Adding login tokens...
docker exec zionix-be-v1-redis-1 redis-cli SETEX login_token:2 600 "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.sample_token_for_jane"

echo.
echo Verifying data...
docker exec zionix-be-v1-redis-1 redis-cli KEYS "user:*"
echo.
docker exec zionix-be-v1-redis-1 redis-cli KEYS "login_token:*"

echo.
echo ✅ Redis populated with realistic user data!
echo.
echo 📋 Available keys:
echo    - user:1 (John Doe - Regular User)
echo    - user:2 (Jane Smith - Admin User)  
echo    - user:3 (Mike Johnson - Inactive User)
echo    - user:email:<EMAIL>
echo    - user:email:<EMAIL>
echo    - user:email:<EMAIL>
echo    - login_token:2 (Jane's login token)
echo.
echo 🔄 Data will expire in 10 minutes (600 seconds)
echo 🌐 Refresh your Redis browser to see the data!

pause
