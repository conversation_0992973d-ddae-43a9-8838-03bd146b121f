# 🪟 Windows Scripts Directory

This directory contains Windows-specific batch scripts (.bat files) for managing the microservices architecture.

## 📁 Scripts

### 🚀 Service Management
- **`start-all-services.bat`** - Start all microservices and dependencies
- **`start-hybrid-services.bat`** - Start services with hybrid gateway architecture

### 🔧 Development Tools
- **`docker-build-with-retry.bat`** - Build Docker images with retry logic for network issues

## 🎯 Usage

### Starting Services

#### Option 1: All Services
```cmd
# Start all services (auth, admin, databases, gateways, monitoring)
scripts\windows\start-all-services.bat
```

#### Option 2: Hybrid Architecture
```cmd
# Start with APISIX (auth) + Envoy (admin) gateways
scripts\windows\start-hybrid-services.bat
```

### Building Docker Images
```cmd
# Build with automatic retry on network failures
scripts\windows\docker-build-with-retry.bat
```

## 🔧 Script Details

### start-hybrid-services.bat
- Starts PostgreSQL databases (auth & admin)
- Starts Redis cache
- Starts Kafka + Zookeeper
- Builds and starts auth-service
- Builds and starts admin-service
- Starts APISIX gateway (for auth-service)
- Starts Envoy gateway (for admin-service)
- Starts monitoring stack (Prometheus, Grafana)

### docker-build-with-retry.bat
- Implements retry logic for Docker builds
- Handles network timeouts and connection issues
- Useful in environments with unstable internet connections

## 🐧 Cross-Platform Alternatives

For Linux/macOS users, equivalent shell scripts are available:
- `scripts/docker-build-with-retry.sh`

**Note**: For Linux/macOS users, you can run the Windows scripts using Wine or create equivalent shell scripts as needed.

## 🛠️ Customization

To modify scripts for your environment:

1. **Database Passwords**: Update in docker-compose files
2. **Port Mappings**: Modify port configurations
3. **Service Dependencies**: Adjust startup order if needed

## 📋 Prerequisites

Before running these scripts:

1. **Docker Desktop** installed and running
2. **Docker Compose** available
3. **PowerShell** execution policy allows script execution:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

## 🔍 Troubleshooting

### Common Issues

1. **Permission Denied**:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **Docker Not Running**:
   - Start Docker Desktop
   - Verify with: `docker --version`

3. **Port Conflicts**:
   - Check if ports 8001, 8002, 5432, 6379, 9092 are available
   - Stop conflicting services or modify port mappings

### Logs and Debugging

View service logs:
```cmd
# View all service logs
docker-compose -f docker-compose.hybrid.yml logs

# View specific service logs
docker-compose -f docker-compose.hybrid.yml logs auth-service
```

## 🎯 Next Steps

After running the scripts:

1. **Verify Services**: Check health endpoints
2. **Access UIs**: 
   - Swagger: http://localhost:8002/docs
   - pgAdmin: http://localhost:5050
   - Grafana: http://localhost:3000
3. **Test APIs**: Use provided test scripts or Postman collections
