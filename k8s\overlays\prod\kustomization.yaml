apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: zionix

bases:
  - ../../base

resources:
  - namespace.yaml

patchesStrategicMerge:
  - admin-service-patch.yaml
  - auth-service-patch.yaml

configMapGenerator:
  - name: admin-service-config
    behavior: merge
    literals:
      - ENVIRONMENT=production
      - LOG_LEVEL=info

secretGenerator:
  - name: admin-service-secrets
    behavior: merge
    files:
      - secrets/postgres-credentials.txt
  - name: jwt-secrets
    behavior: merge
    files:
      - secrets/jwt-secret-key.txt

images:
  - name: admin-service
    newName: zionix/admin-service
    newTag: latest
  - name: auth-service
    newName: zionix/auth-service
    newTag: latest