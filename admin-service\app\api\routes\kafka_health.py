from fastapi import APIRouter, HTTPException
from app.events.kafka_client import check_kafka_health

router = APIRouter()

@router.get("/kafka/health", tags=["Kafka"])
async def kafka_health_check():
    """
    Kafka health check endpoint
    """
    healthy = await check_kafka_health()
    if not healthy:
        raise HTTPException(status_code=503, detail="Kafka is not healthy")
    return {"status": "Kafka is healthy"}
