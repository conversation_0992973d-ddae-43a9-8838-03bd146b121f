"""update users table to use status and action columns

Revision ID: 007
Revises: 006
Create Date: 2025-07-14 14:31:00

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '007'
down_revision = '006'
branch_labels = None
depends_on = None


def upgrade():
    # Rename is_active to status (if it exists)
    try:
        op.alter_column('users', 'is_active', new_column_name='status')
    except:
        # Column might not exist or already renamed
        pass
    
    # Drop is_admin column (if it exists) and add action column
    try:
        op.drop_column('users', 'is_admin')
    except:
        # Column might not exist
        pass
        
    # Add action column if it doesn't exist
    try:
        op.add_column('users', sa.Column('action', sa.String(length=50), nullable=True))
    except:
        # Column might already exist
        pass


def downgrade():
    # Reverse the changes
    try:
        op.alter_column('users', 'status', new_column_name='is_active')
    except:
        pass
        
    try:
        op.drop_column('users', 'action')
    except:
        pass
        
    try:
        op.add_column('users', sa.Column('is_admin', sa.<PERSON>(), nullable=True))
    except:
        pass
