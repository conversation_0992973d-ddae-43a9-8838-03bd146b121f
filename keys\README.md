# 🔐 Encryption Keys Directory

This directory contains the RSA key pair used for hybrid encryption across the microservices.

## 📁 Files

- **`private.pem`** - RSA private key (2048-bit) for decryption
- **`public.pem`** - RSA public key (2048-bit) for encryption

## 🔒 Security Notes

⚠️ **IMPORTANT SECURITY CONSIDERATIONS:**

1. **Private Key Security**:
   - The `private.pem` file contains sensitive cryptographic material
   - Never commit this file to version control in production
   - Ensure proper file permissions (600 or 400)
   - Store securely in production environments

2. **Production Deployment**:
   - Use environment variables or secure key management systems
   - Consider using Docker secrets or Kubernetes secrets
   - Rotate keys regularly according to security policies

3. **Development vs Production**:
   - These keys are for development/testing purposes
   - Generate new keys for each environment
   - Use different keys for staging and production

## 🔧 Key Generation

To generate new RSA key pairs:

```bash
# Generate private key (2048-bit)
openssl genrsa -out private.pem 2048

# Generate public key from private key
openssl rsa -in private.pem -pubout -out public.pem

# Verify key pair
openssl rsa -in private.pem -check
```

## 🏗️ Usage in Services

### Auth Service
- **Path**: `/app/keys/private.pem`, `/app/keys/public.pem`
- **Purpose**: User data encryption, JWT token signing
- **Configuration**: `auth-service/app/core/config.py`

### Admin Service
- **Path**: `/app/keys/private.pem`, `/app/keys/public.pem`
- **Purpose**: Application data encryption, secure communications
- **Configuration**: `admin-service/app/core/hybrid_encryption.py`

## 🐳 Docker Integration

The keys are mounted as volumes in Docker containers:

```yaml
volumes:
  - ./keys/private.pem:/app/keys/private.pem
  - ./keys/public.pem:/app/keys/public.pem
```

## 🔄 Key Rotation

For key rotation in production:

1. Generate new key pair
2. Update environment variables or secrets
3. Restart services with new keys
4. Update any cached public keys in client applications

## 📋 File Permissions

Recommended file permissions:

```bash
# Private key - read-only for owner
chmod 600 private.pem

# Public key - readable by group
chmod 644 public.pem
```
