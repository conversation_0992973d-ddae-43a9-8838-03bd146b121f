@echo off
REM ZCare Hybrid Gateway Services Startup Script
REM APISIX for Auth Service + Envoy for Admin Service

echo 🚀 Starting ZCare Hybrid Gateway Services...
echo 📋 Architecture:
echo    - APISIX Gateway (port 9080) → Auth Service
echo    - Envoy Gateway (port 10000) → Admin Service
echo    - Redis <PERSON>ache (port 6379)
echo    - PostgreSQL Databases (ports 5433, 5434)
echo    - pgAdmin (port 5050)
echo    - Kafka + Zookeeper (ports 9092, 2181)
echo    - Prometheus (port 9090) + <PERSON><PERSON> (port 3000)
echo.

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    pause
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose not found. Please install docker-compose.
    pause
    exit /b 1
)

REM Stop any existing services
echo 🛑 Stopping existing services...
docker-compose -f docker-compose.hybrid.yml down --remove-orphans

REM Clean up
echo 🧹 Cleaning up...
docker system prune -f --volumes

REM Start services
echo 🔄 Starting hybrid gateway services...
docker-compose -f docker-compose.hybrid.yml up -d --build

REM Wait for services to start
echo ⏳ Waiting for services to initialize (60 seconds)...
timeout /t 60 /nobreak >nul

REM Check service health
echo 🔍 Checking service health...

curl -f -s http://localhost:9080/health >nul 2>&1
if errorlevel 1 (
    echo ❌ APISIX Gateway is not responding
) else (
    echo ✅ APISIX Gateway is healthy
)

curl -f -s http://localhost:10000/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Envoy Gateway is not responding
) else (
    echo ✅ Envoy Gateway is healthy
)

curl -f -s http://localhost:8002/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Auth Service is not responding
) else (
    echo ✅ Auth Service is healthy
)

curl -f -s http://localhost:8001/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Admin Service is not responding
) else (
    echo ✅ Admin Service is healthy
)

echo.
echo 📊 Service Status:
docker-compose -f docker-compose.hybrid.yml ps

echo.
echo 🎉 Hybrid Gateway Services Started!
echo.
echo 🌐 Access Points:
echo    - APISIX Gateway (Auth):    http://localhost:9080
echo    - Envoy Gateway (Admin):    http://localhost:10000
echo    - Auth Service Direct:      http://localhost:8002
echo    - Admin Service Direct:     http://localhost:8001
echo    - pgAdmin:                  http://localhost:5050
echo    - Prometheus:               http://localhost:9090
echo    - Grafana:                  http://localhost:3000
echo.
echo 📚 API Documentation:
echo    - Auth Service Swagger:     http://localhost:8002/docs
echo    - Admin Service Swagger:    http://localhost:8001/docs
echo.
echo 🔧 Gateway Admin Interfaces:
echo    - APISIX Control API:       http://localhost:9092
echo    - Envoy Admin Interface:    http://localhost:9901
echo.
echo 🧪 Test Commands:
echo    # Test APISIX (Auth Service)
echo    curl http://localhost:9080/auth/health
echo    curl http://localhost:9080/api/v1/auth/health
echo.
echo    # Test Envoy (Admin Service)
echo    curl http://localhost:10000/admin/health
echo    curl http://localhost:10000/api/v1/admin/health
echo.
echo 📋 Credentials:
echo    - pgAdmin: <EMAIL> / admin123
echo    - Database: postgres / Arunnathan
echo.
pause
