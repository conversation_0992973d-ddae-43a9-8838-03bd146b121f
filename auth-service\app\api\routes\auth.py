from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, Form
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
import asyncio
import logging

from app.core.config import settings
from app.core.jwt import create_refresh_token
from app.core.email import send_otp_email, send_password_reset_confirmation_email
from app.db.session import get_db
from app.crud.user import authenticate_user, get_user_by_email, get_user, save_login_token, update_user_password_by_email
from app.crud.otp import create_otp, verify_otp, use_otp
from app.schemas.token import Token
from app.schemas.user import UserResponse
from app.schemas.password_reset import (
    ForgotPasswordRequest, 
    ForgotPasswordResponse,
    VerifyOTPRequest,
    VerifyOTPResponse,
    ResetPasswordRequest,
    ResetPasswordResponse
)
from app.events.producers.user_events import publish_user_login_event

router = APIRouter(tags=["authentication"])

@router.post("/login", response_model=Token)
async def login_for_refresh_token(
    request: Request,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """OAuth2 compatible token login, get a refresh token for future requests.

    Note: Use your email address in the 'username' field below.
    """
    user = await authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    refresh_token_expires = timedelta(minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES)
    refresh_token = create_refresh_token(
        subject=user.email, expires_delta=refresh_token_expires  # Use email instead of user.id
    )

    # Save the login token to the database
    token_saved = await save_login_token(db, user.id, refresh_token)
    if not token_saved:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save login token"
        )

    # Publish login event
    await publish_user_login_event(
        user_id=user.id,
        email=user.email,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent")
    )

    return {"refresh_token": refresh_token, "token_type": "bearer"}

@router.get("/debug/users")
async def debug_users(db: Session = Depends(get_db)):
    """Debug endpoint to check users in database"""
    from app.models.user import User
    users = db.query(User).all()
    return {"total_users": len(users), "users": [{"id": u.id, "email": u.email} for u in users]}

@router.get("/test-route")
async def test_route():
    """Simple test route to verify route registration"""
    print("DEBUG: test_route called!")
    return {"message": "Test route is working", "status": "success"}

from app.schemas.user import UserCreate

@router.post("/register", response_model=UserResponse)
async def register_user(
    email: str = Form(...),
    password: str = Form(...),
    confirm_password: str = Form(...),
    first_name: str = Form(...),
    last_name: str = Form(...),
    action: str = Form(None),
    db: Session = Depends(get_db)
):
    """Register new user with email and password"""
    try:
        # Check if user already exists
        existing_user = await get_user_by_email(db, email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Create UserCreate object
        user_create = UserCreate(
            email=email,
            password=password,
            confirm_password=confirm_password,
            first_name=first_name,
            last_name=last_name,
            action=action
        )

        # Create new user
        from app.crud.user import create_user
        new_user = await create_user(db, user_create)

        return new_user  # Let FastAPI use from_attributes for UserResponse

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Registration error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not register user"
        )

@router.post("/test-verify")
async def test_verify(token: str = Query(..., description="Token to verify")):
    """Test verify endpoint to debug route issues"""
    print(f"DEBUG: test_verify called with token: {token}")
    return {"message": "Test verify is working", "token_received": token[:20] + "..."}

@router.post("/verify-token", response_model=UserResponse)
async def verify_token(token: str = Query(..., description="JWT token to verify"), db: Session = Depends(get_db)):
    """Verify a token and return the user information"""
    try:
        # Decode JWT token
        from jose import jwt
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        email = payload.get("sub")  # Contains email
        if email is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Look up user by email
        user = await get_user_by_email(db, email=email)
        if user is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found",
            )

        # Verify that the token matches the stored login token
        if user.login_token != token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has been invalidated",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return user

    except jwt.JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in verify_token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )

@router.post("/forgot-password", response_model=ForgotPasswordResponse)
async def forgot_password(
    request: ForgotPasswordRequest,
    db: Session = Depends(get_db)
):
    """Request password reset by sending OTP to email"""
    try:
        # Check if user exists
        user = await get_user_by_email(db, email=request.email)
        if user:
            # Create OTP only for valid users
            otp = create_otp(db, user_id=user.id, purpose="password_reset")
            
            # Send OTP email
            email_sent = await send_otp_email(
                email=request.email,
                otp_code=otp.otp_code,
                user_name=f"{user.first_name} {user.last_name}"
            )
            
            if not email_sent:
                logging.error(f"Failed to send OTP email to {request.email}")
        else:
            # Simulate processing time to prevent email enumeration
            await asyncio.sleep(0.5)
        
        # Return consistent response regardless of email existence
        return ForgotPasswordResponse(
            message="If the email exists in our system, you will receive an OTP shortly.",
            email=request.email
        )
        
    except Exception as e:
        logging.error(f"Error in forgot_password: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/verify-otp", response_model=VerifyOTPResponse)
async def verify_otp_endpoint(
    request: VerifyOTPRequest,
    db: Session = Depends(get_db)
):
    """Verify OTP for password reset"""
    try:
        # Check if user exists
        user = await get_user_by_email(db, email=request.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Verify OTP
        is_valid = verify_otp(db, user_id=user.id, otp_code=request.otp_code, purpose="password_reset")
        
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired OTP"
            )
        
        return VerifyOTPResponse(
            message="OTP verified successfully. You can now reset your password.",
            email=request.email,
            is_valid=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in verify_otp: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/reset-password", response_model=ResetPasswordResponse)
async def reset_password(
    request: ResetPasswordRequest,
    db: Session = Depends(get_db)
):
    """Reset password using verified OTP"""
    try:
        # Validate passwords match
        request.validate_passwords_match()
        
        # Check if user exists
        user = await get_user_by_email(db, email=request.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Verify and use OTP (mark as used)
        otp_used = use_otp(db, user_id=user.id, otp_code=request.otp_code, purpose="password_reset")
        
        if not otp_used:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired OTP"
            )
        
        # Update password
        password_updated = await update_user_password_by_email(db, email=request.email, new_password=request.new_password)
        
        if not password_updated:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update password"
            )
        
        # Send confirmation email
        await send_password_reset_confirmation_email(
            email=request.email,
            user_name=f"{user.first_name} {user.last_name}"
        )
        
        # Publish password changed event
        from app.events.producers.user_events import publish_user_password_changed_event
        await publish_user_password_changed_event(
            user_id=user.id,
            email=user.email
        )
        
        return ResetPasswordResponse(
            message="Password reset successfully. You can now log in with your new password.",
            email=request.email
        )
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logging.error(f"Error in reset_password: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


