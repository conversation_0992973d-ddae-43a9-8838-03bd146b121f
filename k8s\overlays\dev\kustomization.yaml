apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: zionix-dev

bases:
  - ../../base

resources:
  - namespace.yaml

patchesStrategicMerge:
  - admin-service-patch.yaml
  - auth-service-patch.yaml

configMapGenerator:
  - name: admin-service-config
    behavior: merge
    literals:
      - ENVIRONMENT=development
      - LOG_LEVEL=debug

secretGenerator:
  - name: admin-service-secrets
    behavior: merge
    literals:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=devpassword
  - name: jwt-secrets
    behavior: merge
    literals:
      - SECRET_KEY=dev_jwt_secret_key_for_testing_only

images:
  - name: admin-service
    newName: zionix/admin-service
    newTag: dev
  - name: auth-service
    newName: zionix/auth-service
    newTag: dev