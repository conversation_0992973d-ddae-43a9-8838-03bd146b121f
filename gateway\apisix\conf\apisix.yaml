routes:
  - id: auth-api-routes
    name: "Auth Service API Routes"
    uri: /api/v1/auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
      checks:
        active:
          type: http
          http_path: "/health"
          healthy:
            interval: 30
            http_statuses: [200, 302]
            successes: 2
          unhealthy:
            interval: 30
            http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
            http_failures: 3
            timeouts: 3
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      limit-req:
        rate: 10
        burst: 30
        rejected_code: 429
        nodelay: true
      proxy-cache:
        cache_zone: "auth_cache"
        cache_key: ["$host", "$request_uri"]
        cache_bypass: ["$arg_nocache"]
        cache_method: ["GET", "HEAD"]
        cache_http_status: [200, 301, 404]
        hide_cache_headers: true
        cache_ttl: 300
      proxy-rewrite:
        regex_uri: ["^/api/v1/auth/(.*)", "/api/v1/$1"]
      prometheus:
        prefer_name: true

  - id: auth-direct-routes
    name: "Auth Service Direct Routes"
    uri: /auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
      checks:
        active:
          type: http
          http_path: "/health"
          healthy:
            interval: 30
            http_statuses: [200, 302]
            successes: 2
          unhealthy:
            interval: 30
            http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
            http_failures: 3
            timeouts: 3
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      limit-req:
        rate: 10
        burst: 30
        rejected_code: 429
        nodelay: true
      proxy-cache:
        cache_zone: "auth_cache"
        cache_key: ["$host", "$request_uri"]
        cache_bypass: ["$arg_nocache"]
        cache_method: ["GET", "HEAD"]
        cache_http_status: [200, 301, 404]
        hide_cache_headers: true
        cache_ttl: 300
      proxy-rewrite:
        regex_uri: ["^/auth/(.*)", "/$1"]
      prometheus:
        prefer_name: true

  - id: health-check
    name: "Health Check Route"
    uri: /health
    plugins:
      echo:
        body: "healthy"

  - id: root-route
    name: "Root Route"
    uri: /
    plugins:
      echo:
        body: '{"message": "ZCare API Gateway - APISIX (Auth Service)", "version": "1.0.0", "services": ["auth-service"], "gateway_type": "apisix", "port": 9080}'

upstreams:
  - id: auth-service
    name: "Auth Service Upstream"
    type: roundrobin
    nodes:
      "auth-service:8000": 1
    checks:
      active:
        type: http
        http_path: "/health"
        healthy:
          interval: 30
          http_statuses: [200, 302]
          successes: 2
        unhealthy:
          interval: 30
          http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
          http_failures: 3
          timeouts: 3
    timeout:
      connect: 6
      send: 6
      read: 6

global_rules:
  - id: global-cors
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600

#END
