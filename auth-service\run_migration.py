#!/usr/bin/env python3
"""
Script to run database migrations for the auth service
"""
import subprocess
import sys
import os

def run_migration():
    """Run the database migration"""
    try:
        # Change to the auth-service directory
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        print("Running database migration...")
        
        # Run alembic upgrade
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Migration completed successfully!")
            print(result.stdout)
        else:
            print("❌ Migration failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error running migration: {e}")
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)