from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.db.session import get_db
from app.schemas.user import UserCreate, UserUpdate, UserResponse
from app.crud.user import create_user, get_user, get_user_by_email, get_users, update_user, delete_user, update_user_by_email, delete_user_by_email
from app.api.dependencies import get_current_user
from app.models.user import User

router = APIRouter(prefix="/users", tags=["users"])

@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(user: UserCreate, db: Session = Depends(get_db)):
    """Register a new user"""
    db_user = await create_user(db=db, user=user)
    return db_user

@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """Get current user profile"""
    return current_user

@router.put("/me", response_model=UserResponse)
async def update_my_profile(user: UserUpdate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Update current user's profile"""
    updated_user = await update_user_by_email(db=db, email=current_user.email, user=user)
    return updated_user



@router.get("/by-email/{email}", response_model=UserResponse)
async def read_user_by_email(email: str, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Get user by email (admin only or self)"""
    if current_user.action != "admin" and current_user.email != email:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    db_user = await get_user_by_email(db=db, email=email)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user

@router.get("/{user_id}", response_model=UserResponse)
async def read_user(user_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Get user by ID (admin only) - Legacy endpoint"""
    if current_user.action != "admin":
        raise HTTPException(status_code=403, detail="Not enough permissions")
    db_user = await get_user(db=db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user



@router.get("/", response_model=List[UserResponse])
async def read_users(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Get all users with pagination (admin only)"""
    if current_user.action != "admin":  # Use action field instead of is_admin
        raise HTTPException(status_code=403, detail="Not enough permissions")
    users = get_users(db=db, skip=skip, limit=limit)
    return users

@router.put("/by-email/{email}", response_model=UserResponse)
async def update_user_profile_by_email(email: str, user: UserUpdate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Update a user profile by email (admin or self only)"""
    if current_user.email != email and current_user.action != "admin":
        raise HTTPException(status_code=403, detail="Not enough permissions")
    db_user = await get_user_by_email(db=db, email=email)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    updated_user = await update_user_by_email(db=db, email=email, user=user)
    return updated_user

@router.put("/{user_id}", response_model=UserResponse)
async def update_user_profile(user_id: int, user: UserUpdate, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Update a user profile by ID (admin or self only) - Legacy endpoint"""
    if current_user.id != user_id and current_user.action != "admin":
        raise HTTPException(status_code=403, detail="Not enough permissions")
    db_user = await get_user(db=db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    updated_user = await update_user(db=db, user_id=user_id, user=user)
    return updated_user

@router.delete("/by-email/{email}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_account_by_email(email: str, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Delete a user account by email (admin only)"""
    if current_user.action != "admin":
        raise HTTPException(status_code=403, detail="Not enough permissions")
    db_user = await get_user_by_email(db=db, email=email)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    await delete_user_by_email(db=db, email=email)
    return None

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_account(user_id: int, db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    """Delete a user account by ID (admin only) - Legacy endpoint"""
    if current_user.action != "admin":
        raise HTTPException(status_code=403, detail="Not enough permissions")
    db_user = await get_user(db=db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    await delete_user(db=db, user_id=user_id)
    return None
