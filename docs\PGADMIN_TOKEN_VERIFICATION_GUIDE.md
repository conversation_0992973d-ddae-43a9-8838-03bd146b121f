# 🔍 pgAdmin Token Verification Guide

## 📊 **How to Check Access Tokens in pgAdmin**

### **Step 1: Access pgAdmin**
- **URL**: http://localhost:5050 (already opened)
- **Email**: `<EMAIL>`
- **Password**: `admin`

### **Step 2: Connect to Auth Database**
1. **Expand Server** → **PostgreSQL 13** → **Databases**
2. **Click on** `auth_db` database
3. **Expand** `auth_db` → **Schemas** → **public** → **Tables**
4. **Right-click on** `users` table
5. **Select** "View/Edit Data" → "All Rows"

### **Step 3: Check login_token Column**
You should see a table with these columns:
- `id` - User ID
- `email` - User email address
- `first_name` - User's first name
- `last_name` - User's last name
- `hashed_password` - Encrypted password
- `status` - User status (true/false)
- `action` - User action field
- **`login_token`** - **This contains the JWT access token**
- `created_at` - User creation timestamp
- `updated_at` - Last update timestamp

### **Step 4: Verify Token Storage**

#### **Before Login:**
```
login_token: NULL (or empty)
```

#### **After Successful Login:**
```
login_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.signature_here
```

## 🧪 **Test Token Storage Process**

### **Method 1: Create User and Login via Swagger**

1. **Register New User** (Swagger UI):
   ```json
   {
     "email": "<EMAIL>",
     "first_name": "Token",
     "last_name": "Test",
     "password": "TestPassword123",
     "confirm_password": "TestPassword123",
     "status": true
   }
   ```

2. **Check pgAdmin** - User should appear with `login_token: NULL`

3. **Login via Swagger**:
   - username: `<EMAIL>`
   - password: `TestPassword123`

4. **Check pgAdmin Again** - `login_token` should now contain JWT token

5. **Refresh pgAdmin** - Click refresh button or F5 to see updated data

### **Method 2: SQL Query Method**

In pgAdmin, you can also run SQL queries:

1. **Click** "Query Tool" button (SQL icon)
2. **Run this query** to see all users and their tokens:

```sql
SELECT 
    id,
    email,
    first_name,
    last_name,
    status,
    CASE 
        WHEN login_token IS NULL THEN 'No Token'
        WHEN LENGTH(login_token) > 50 THEN CONCAT(LEFT(login_token, 50), '...')
        ELSE login_token
    END as token_preview,
    created_at,
    updated_at
FROM users
ORDER BY updated_at DESC;
```

3. **Run this query** to see only users with tokens:

```sql
SELECT 
    id,
    email,
    first_name,
    last_name,
    login_token,
    updated_at
FROM users 
WHERE login_token IS NOT NULL
ORDER BY updated_at DESC;
```

4. **Run this query** to see token details:

```sql
SELECT 
    email,
    LENGTH(login_token) as token_length,
    LEFT(login_token, 100) as token_start,
    updated_at as last_login
FROM users 
WHERE login_token IS NOT NULL;
```

## 🔐 **Understanding JWT Token Structure**

### **Token Format:**
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.signature_here
```

### **Token Parts:**
1. **Header** (eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9)
2. **Payload** (***************************************************************)
3. **Signature** (signature_here)

### **Decoded Payload Contains:**
```json
{
  "sub": "<EMAIL>",  // User email
  "exp": 1719243600              // Expiration timestamp
}
```

## 📊 **Database Schema Verification**

### **Check Table Structure:**
```sql
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;
```

### **Expected login_token Column:**
- **Column Name**: `login_token`
- **Data Type**: `text`
- **Nullable**: `YES`
- **Default**: `NULL`

## 🔄 **Token Lifecycle in Database**

### **1. User Registration:**
```sql
INSERT INTO users (email, first_name, last_name, hashed_password, status, login_token)
VALUES ('<EMAIL>', 'John', 'Doe', 'hashed_pwd', true, NULL);
```

### **2. User Login (Token Save):**
```sql
UPDATE users 
SET login_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    updated_at = NOW()
WHERE id = 1;
```

### **3. Token Usage:**
- Token is read from database for validation
- Token is cached in Redis for performance
- Database serves as source of truth

## 🚨 **Troubleshooting**

### **If Token Not Appearing:**

1. **Check Login Success:**
   - Ensure login returns 200 OK
   - Verify access_token in response

2. **Refresh pgAdmin:**
   - Click refresh button
   - Press F5
   - Re-run query

3. **Check Error Handling:**
   - Look for warning messages in logs
   - Token save might fail gracefully

4. **Verify Database Connection:**
   ```sql
   SELECT NOW(); -- Should return current timestamp
   ```

### **If Token Appears Truncated:**
- pgAdmin might truncate long text fields
- Use SQL queries to see full token
- Token length should be ~200-300 characters

## ✅ **Success Indicators**

### **What You Should See:**
- ✅ **User Record**: Created after registration
- ✅ **NULL Token**: Initially after registration
- ✅ **JWT Token**: After successful login
- ✅ **Updated Timestamp**: Changes after login
- ✅ **Token Format**: Proper JWT structure (3 parts separated by dots)

### **Token Validation:**
- ✅ **Length**: ~200-300 characters
- ✅ **Format**: `header.payload.signature`
- ✅ **Content**: Contains user email in payload
- ✅ **Expiration**: Has valid expiration time

## 🎯 **Next Steps**

1. **✅ Verify Token Storage** - Check pgAdmin after login
2. **✅ Test Token Usage** - Use token for API calls
3. **✅ Monitor Token Updates** - See changes after new logins
4. **✅ Check Token Expiration** - Verify lifecycle management

## 🌟 **Database Performance**

### **Redis Caching Benefits:**
- **Fast Token Lookup**: Cached for quick access
- **Reduced DB Load**: Less database queries
- **Better Performance**: 50-80% faster token validation

### **Check Cache Status:**
```bash
curl http://localhost:8002/health/cache/stats
```

---

**🎉 Your access tokens are now being properly stored in PostgreSQL and cached in Redis for optimal performance! 🚀**
