version: '3.8'

services:
  envoy-gateway:
    image: envoyproxy/envoy:v1.27-latest
    ports:
      - "8080:80"
      - "9901:9901"  # Admin interface
    volumes:
      - ./envoy.yaml:/etc/envoy/envoy.yaml:ro
    depends_on:
      - admin-service
      - auth-service
    networks:
      - zcare-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9901/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Admin Service
  admin-service:
    build: ../../admin-service
    environment:
      - POSTGRES_SERVER=postgres-admin
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=<PERSON><PERSON><PERSON><PERSON>
      - POSTGRES_DB=admin_service
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - AUTH_SERVICE_URL=http://auth-service:8000
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
    depends_on:
      postgres-admin:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      kafka:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Auth Service
  auth-service:
    build: ../../auth-service
    environment:
      - POSTGRES_SERVER=postgres-auth
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
    depends_on:
      postgres-auth:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Databases
  postgres-admin:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-admin-data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d admin_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  postgres-auth:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-auth-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d auth_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    healthcheck:
      test: ["CMD", "bash", "-c", "echo 'ruok' | nc localhost 2181"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  kafka:
    image: confluentinc/cp-kafka:7.0.1
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics --bootstrap-server localhost:9092 --list"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

volumes:
  postgres-admin-data:
  postgres-auth-data:

networks:
  zcare-network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1450