apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-service
spec:
  replicas: 3
  template:
    spec:
      containers:
      - name: admin-service
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        env:
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: admin-service-config
              key: ENVIRONMENT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: admin-service-config
              key: LOG_LEVEL