@echo off
echo Testing Redis Connection and Populating User Data
echo ================================================

echo.
echo 1. Testing Redis connection...
redis-cli ping
if %errorlevel% neq 0 (
    echo Redis is not running on localhost:6379
    echo Please start Redis server or Docker services
    pause
    exit /b 1
)

echo ✅ Redis is running!
echo.

echo 2. Populating Redis with sample user data...

REM User 1 - John Doe
echo Setting user:1...
redis-cli SETEX "user:1" 300 "{\"id\":1,\"email\":\"<EMAIL>\",\"first_name\":\"John\",\"last_name\":\"Doe\",\"hashed_password\":\"$2b$12$example_hash\",\"status\":true,\"action\":\"user\",\"login_token\":null,\"created_at\":\"2024-01-17T10:00:00\",\"updated_at\":null}"

echo Setting user:email:<EMAIL>...
redis-cli SETEX "user:email:<EMAIL>" 300 "{\"id\":1,\"email\":\"<EMAIL>\",\"first_name\":\"John\",\"last_name\":\"Doe\",\"hashed_password\":\"$2b$12$example_hash\",\"status\":true,\"action\":\"user\",\"login_token\":null,\"created_at\":\"2024-01-17T10:00:00\",\"updated_at\":null}"

REM User 2 - Jane Smith
echo Setting user:2...
redis-cli SETEX "user:2" 300 "{\"id\":2,\"email\":\"<EMAIL>\",\"first_name\":\"Jane\",\"last_name\":\"Smith\",\"hashed_password\":\"$2b$12$another_hash\",\"status\":true,\"action\":\"admin\",\"login_token\":\"sample_token_123\",\"created_at\":\"2024-01-17T10:00:00\",\"updated_at\":\"2024-01-17T10:30:00\"}"

echo Setting user:email:<EMAIL>...
redis-cli SETEX "user:email:<EMAIL>" 300 "{\"id\":2,\"email\":\"<EMAIL>\",\"first_name\":\"Jane\",\"last_name\":\"Smith\",\"hashed_password\":\"$2b$12$another_hash\",\"status\":true,\"action\":\"admin\",\"login_token\":\"sample_token_123\",\"created_at\":\"2024-01-17T10:00:00\",\"updated_at\":\"2024-01-17T10:30:00\"}"

REM Login token for Jane
echo Setting login_token:2...
redis-cli SETEX "login_token:2" 300 "sample_token_123"

echo.
echo 3. Verifying data was stored...
echo.

echo Keys matching 'user:*':
redis-cli KEYS "user:*"

echo.
echo Keys matching 'login_token:*':
redis-cli KEYS "login_token:*"

echo.
echo 4. Testing data retrieval...
echo.

echo Getting user:1:
redis-cli GET "user:1"

echo.
echo Getting user by email:
redis-cli GET "user:email:<EMAIL>"

echo.
echo ✅ Redis population completed!
echo.
echo 📝 Instructions:
echo 1. Refresh your Redis browser/client
echo 2. Look for keys starting with 'user:' and 'login_token:'
echo 3. The data will expire in 5 minutes (300 seconds)
echo.

pause
