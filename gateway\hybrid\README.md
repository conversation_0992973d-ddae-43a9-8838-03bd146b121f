# 🔀 ZCare Hybrid Gateway Setup

This directory contains a **hybrid gateway configuration** that uses:
- **APISIX** for handling **auth-service** routes (Port 8080)
- **Envoy** for handling **admin-service** routes (Port 8081)

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Apps   │    │                 │    │                 │
│                 │────│  APISIX Gateway │────│  Auth Service   │
│ Web/Mobile/API  │    │   Port: 8080    │    │   Port: 8000    │
│                 │    └─────────────────┘    └─────────────────┘
│                 │    ┌─────────────────┐    ┌─────────────────┐
│                 │────│  Envoy Gateway  │────│  Admin Service  │
│                 │    │   Port: 8081    │    │   Port: 8000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Windows
```cmd
cd gateway/hybrid
start-hybrid.bat
```

### Linux/macOS
```bash
cd gateway/hybrid
chmod +x start-hybrid.sh
./start-hybrid.sh
```

### Manual Start
```bash
cd gateway/hybrid
docker-compose up -d
```

## 🔗 Service URLs

| Service | Gateway | URL | Purpose |
|---------|---------|-----|---------|
| **Auth Service** | APISIX | http://localhost:8080 | Authentication, Users, JWT |
| **Admin Service** | Envoy | http://localhost:8081 | Domains, Applications, Admin |

## 📊 Management Interfaces

| Interface | URL | Description |
|-----------|-----|-------------|
| APISIX Metrics | http://localhost:9091 | Prometheus metrics |
| Envoy Admin | http://localhost:9901 | Envoy administration |

## 🛣️ API Routes

### 🔐 Auth Service Routes (APISIX - Port 8080)
```
POST /api/v1/auth/login              - User authentication
POST /api/v1/auth/verify-token       - Token verification  
GET  /api/v1/auth/users/me           - Get current user
POST /api/v1/auth/users/             - User registration
GET  /api/v1/auth/encryption/public-key - Get public key
POST /api/v1/auth/encryption/encrypt - Encrypt data
POST /api/v1/auth/encryption/decrypt - Decrypt data

# Direct access routes
GET  /auth/health                    - Auth service health
GET  /auth/docs                      - Auth service docs
```

### 🏢 Admin Service Routes (Envoy - Port 8081)
```
POST /api/v1/admin/domains/create_domain/        - Create domain
GET  /api/v1/admin/domains/get_all_domains/      - List domains
GET  /api/v1/admin/domains/get_domain/{id}       - Get domain
PUT  /api/v1/admin/domains/update_domain/{id}    - Update domain
DELETE /api/v1/admin/domains/delete_domain/{id}  - Delete domain

POST /api/v1/admin/applications/create_application/     - Create application
GET  /api/v1/admin/applications/get_all_applications/   - List applications
GET  /api/v1/admin/applications/get_application/{id}    - Get application

GET  /api/v1/admin/encryption/public-key        - Get public key
POST /api/v1/admin/encryption/encrypt           - Encrypt data
POST /api/v1/admin/encryption/decrypt           - Decrypt data

# Direct access routes
GET  /admin/health                               - Admin service health
GET  /admin/docs                                 - Admin service docs
```

## 🧪 Testing

### Automated Testing
```bash
cd gateway/hybrid
python test-hybrid.py
```

### Manual Testing
```bash
# Test Auth Gateway (APISIX)
curl http://localhost:8080/health
curl http://localhost:8080/api/v1/auth/encryption/public-key

# Test Admin Gateway (Envoy)  
curl http://localhost:8081/health
curl http://localhost:8081/api/v1/admin/encryption/public-key

# Test gateway info
curl http://localhost:8080/
curl http://localhost:8081/
```

## ⚙️ Configuration Files

| File | Purpose |
|------|---------|
| `docker-compose.yml` | Main deployment configuration |
| `apisix-config.yaml` | APISIX gateway configuration |
| `apisix-auth.yaml` | APISIX routes for auth service |
| `envoy-admin.yaml` | Envoy configuration for admin service |

## 🔧 Gateway Features

### APISIX (Auth Service)
- **Rate Limiting**: 5 req/s, burst 20
- **CORS**: Permissive for development
- **Health Checks**: Active monitoring
- **Metrics**: Prometheus integration
- **Plugin System**: Extensible architecture

### Envoy (Admin Service)  
- **Rate Limiting**: 10 req/s, burst 50
- **CORS**: Permissive for development
- **Health Checks**: Active monitoring
- **Admin Interface**: Comprehensive management
- **Observability**: Built-in metrics and tracing

## 🔍 Monitoring

### Service Health
```bash
# Check all services
docker-compose ps

# Check logs
docker-compose logs apisix-auth-gateway
docker-compose logs envoy-admin-gateway
docker-compose logs auth-service
docker-compose logs admin-service
```

### Gateway Metrics
```bash
# APISIX metrics
curl http://localhost:9091/apisix/prometheus/metrics

# Envoy stats
curl http://localhost:9901/stats
```

## 🛑 Stopping Services

```bash
cd gateway/hybrid
docker-compose down

# With volume cleanup
docker-compose down -v
```

## 🔒 Security Configuration

### Rate Limiting
- **Auth Service**: 5 requests/second, burst 20
- **Admin Service**: 10 requests/second, burst 50

### CORS
Both gateways configured with permissive CORS for development:
- Origins: `*`
- Methods: `GET, POST, PUT, DELETE, OPTIONS`
- Headers: Standard + Authorization

### Health Monitoring
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Retries**: 3-5 attempts

## 🚨 Troubleshooting

### Common Issues

**Port Conflicts**
```bash
# Check if ports are in use
netstat -tulpn | grep :8080
netstat -tulpn | grep :8081
```

**Service Not Starting**
```bash
# Check logs
docker-compose logs <service-name>

# Restart specific service
docker-compose restart <service-name>
```

**Gateway Not Responding**
```bash
# Check backend service health
curl http://localhost:8080/health
curl http://localhost:8081/health

# Check gateway admin interfaces
curl http://localhost:9901/ready  # Envoy
curl http://localhost:9091        # APISIX metrics
```

## 🎯 Benefits of Hybrid Setup

### Why APISIX for Auth Service?
- **Dynamic Configuration**: Easy to modify auth rules
- **Plugin Ecosystem**: Rich authentication plugins
- **Performance**: Optimized for high-frequency auth requests
- **Metrics**: Built-in Prometheus integration

### Why Envoy for Admin Service?
- **Advanced Routing**: Complex admin workflow routing
- **Observability**: Comprehensive monitoring and tracing
- **Reliability**: Battle-tested in production environments
- **gRPC Support**: Future-ready for microservices evolution

## 📈 Performance Characteristics

| Metric | APISIX (Auth) | Envoy (Admin) |
|--------|---------------|---------------|
| Latency | Very Low | Low |
| Throughput | Very High | High |
| Memory Usage | Low | Medium |
| CPU Usage | Low | Medium |
| Configuration | Dynamic | Static/Dynamic |

## 🔄 Scaling Considerations

### Horizontal Scaling
```yaml
# Scale auth service
docker-compose up -d --scale auth-service=3

# Scale admin service  
docker-compose up -d --scale admin-service=2
```

### Load Balancing
Both gateways automatically load balance across multiple backend instances.

## 📝 Next Steps

1. **Production Hardening**
   - Configure SSL/TLS certificates
   - Implement stricter CORS policies
   - Add authentication at gateway level

2. **Monitoring Enhancement**
   - Set up Grafana dashboards
   - Configure alerting rules
   - Implement distributed tracing

3. **Performance Optimization**
   - Tune rate limiting based on usage
   - Optimize connection pooling
   - Configure caching strategies

## ✅ Verification Checklist

- [ ] Both gateways start successfully
- [ ] Auth service accessible via APISIX (port 8080)
- [ ] Admin service accessible via Envoy (port 8081)
- [ ] Health checks passing
- [ ] CORS working for frontend integration
- [ ] Rate limiting functioning
- [ ] Metrics collection active
- [ ] All API endpoints responding correctly
