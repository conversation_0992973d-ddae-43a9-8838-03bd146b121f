from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import Optional
import secrets
import string

from app.models.otp import OTP
from app.models.user import User

def generate_otp_code() -> str:
    """Generate a 6-digit OTP code"""
    return ''.join(secrets.choice(string.digits) for _ in range(6))

def create_otp(db: Session, user_id: int, purpose: str = "password_reset") -> OTP:
    """Create a new OTP for a user"""
    # Invalidate any existing OTPs for this user and purpose
    db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.purpose == purpose,
        OTP.is_used == False
    ).update({"is_used": True})
    
    # Create new OTP
    otp_code = generate_otp_code()
    expires_at = datetime.utcnow() + timedelta(minutes=5)  # 5 minutes expiry
    
    db_otp = OTP(
        user_id=user_id,
        otp_code=otp_code,
        purpose=purpose,
        expires_at=expires_at
    )
    
    db.add(db_otp)
    db.commit()
    db.refresh(db_otp)
    return db_otp

def verify_otp(db: Session, user_id: int, otp_code: str, purpose: str = "password_reset") -> bool:
    """Verify an OTP code for a user"""
    otp = db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.otp_code == otp_code,
        OTP.purpose == purpose,
        OTP.is_used == False,
        OTP.expires_at > datetime.utcnow()
    ).first()
    
    return otp is not None

def use_otp(db: Session, user_id: int, otp_code: str, purpose: str = "password_reset") -> bool:
    """Mark an OTP as used after successful verification"""
    otp = db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.otp_code == otp_code,
        OTP.purpose == purpose,
        OTP.is_used == False,
        OTP.expires_at > datetime.utcnow()
    ).first()
    
    if otp:
        otp.is_used = True
        db.commit()
        return True
    return False

def get_valid_otp(db: Session, user_id: int, otp_code: str, purpose: str = "password_reset") -> Optional[OTP]:
    """Get a valid OTP for a user"""
    return db.query(OTP).filter(
        OTP.user_id == user_id,
        OTP.otp_code == otp_code,
        OTP.purpose == purpose,
        OTP.is_used == False,
        OTP.expires_at > datetime.utcnow()
    ).first()

def cleanup_expired_otps(db: Session):
    """Clean up expired OTPs"""
    db.query(OTP).filter(OTP.expires_at < datetime.utcnow()).delete()
    db.commit()