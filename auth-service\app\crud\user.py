from sqlalchemy.orm import Session
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from typing import List, Optional
from datetime import datetime
import logging

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate, UserResponse
from app.core.security import get_password_hash, verify_password
from app.core.cache import cache, get_user_cache_key, get_user_email_cache_key, get_login_token_cache_key
from app.events.producers.user_events import (
    publish_user_created_event,
    publish_user_updated_event,
    publish_user_deleted_event
)

async def get_user(db: Session, user_id: int) -> Optional[User]:
    """Get a user by ID with Redis caching"""
    # Try to get from cache first
    cache_key = get_user_cache_key(user_id)
    cached_user = await cache.get(cache_key)

    if cached_user:
        # Convert cached dict back to User object
        user = User(
            id=cached_user["id"],
            email=cached_user["email"],
            first_name=cached_user["first_name"],
            last_name=cached_user["last_name"],
            hashed_password=cached_user["hashed_password"],
            status=cached_user["status"],
            action=cached_user.get("action"),
            login_token=cached_user.get("login_token")
        )
        # Convert datetime strings back to datetime objects
        if cached_user.get("created_at"):
            user.created_at = datetime.fromisoformat(cached_user["created_at"])
        if cached_user.get("updated_at"):
            user.updated_at = datetime.fromisoformat(cached_user["updated_at"])
        return user

    # If not in cache, get from database
    db_user = db.query(User).filter(User.id == user_id).first()

    if db_user:
        # Cache the user data
        user_dict = {
            "id": db_user.id,
            "email": db_user.email,
            "first_name": db_user.first_name,
            "last_name": db_user.last_name,
            "hashed_password": db_user.hashed_password,
            "status": db_user.status,
            "action": db_user.action,
            "login_token": db_user.login_token,
            "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
            "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
        }
        await cache.set(cache_key, user_dict)
        # Also cache by email for dual lookup
        await cache.set(get_user_email_cache_key(db_user.email), user_dict)

    return db_user

async def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """Get a user by email with Redis caching"""
    # Try to get from cache first
    cache_key = get_user_email_cache_key(email)
    cached_user = await cache.get(cache_key)

    if cached_user:
        # Convert cached dict back to User object
        user = User(
            id=cached_user["id"],
            email=cached_user["email"],
            first_name=cached_user["first_name"],
            last_name=cached_user["last_name"],
            hashed_password=cached_user["hashed_password"],
            status=cached_user["status"],
            action=cached_user.get("action"),
            login_token=cached_user.get("login_token")
        )
        # Convert datetime strings back to datetime objects
        if cached_user.get("created_at"):
            user.created_at = datetime.fromisoformat(cached_user["created_at"])
        if cached_user.get("updated_at"):
            user.updated_at = datetime.fromisoformat(cached_user["updated_at"])
        return user

    # If not in cache, get from database
    db_user = db.query(User).filter(User.email == email).first()

    if db_user:
        # Cache the user data
        user_dict = {
            "id": db_user.id,
            "email": db_user.email,
            "first_name": db_user.first_name,
            "last_name": db_user.last_name,
            "hashed_password": db_user.hashed_password,
            "status": db_user.status,
            "action": db_user.action,
            "login_token": db_user.login_token,
            "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
            "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
        }
        await cache.set(cache_key, user_dict)
        # Also cache by ID for dual lookup
        await cache.set(get_user_cache_key(db_user.id), user_dict)

    return db_user

def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all users with pagination"""
    return db.query(User).offset(skip).limit(limit).all()

async def create_user(db: Session, user: UserCreate) -> User:
    """Create a new user"""
    # Check if email already exists
    db_user = await get_user_by_email(db, email=user.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")

    # Confirm password already validated in Pydantic schema

    # Hash the password
    hashed_password = get_password_hash(user.password)

    # Create new user (only with fields that exist in the User model)
    db_user = User(
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        hashed_password=hashed_password,
        status=user.status
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Cache the new user
    user_dict = {
        "id": db_user.id,
        "email": db_user.email,
        "first_name": db_user.first_name,
        "last_name": db_user.last_name,
        "hashed_password": db_user.hashed_password,
        "status": db_user.status,
        "action": db_user.action,
        "login_token": db_user.login_token,
        "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
        "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
    }
    await cache.set(get_user_cache_key(db_user.id), user_dict)
    await cache.set(get_user_email_cache_key(db_user.email), user_dict)

    # Publish user created event
    await publish_user_created_event(
        user_id=db_user.id,
        email=db_user.email,
        first_name=db_user.first_name,
        last_name=db_user.last_name,
        status=db_user.status,
        action=db_user.action
    )

    return db_user

async def update_user(db: Session, user_id: int, user: UserUpdate) -> User:
    """Update a user by ID"""
    db_user = await get_user(db, user_id=user_id)
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    update_data = user.model_dump(exclude_unset=True)
    updated_fields = list(update_data.keys())

    # Hash the password if it's being updated
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data["password"])
        del update_data["password"]
        updated_fields.append("hashed_password")

    # Check email uniqueness if being updated
    if "email" in update_data and update_data["email"] != db_user.email:
        if await get_user_by_email(db, email=update_data["email"]):
            raise HTTPException(status_code=400, detail="Email already registered")

    # Store old email for cache invalidation
    old_email = db_user.email

    # No encrypted fields in current User model

    for key, value in update_data.items():
        setattr(db_user, key, value)

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Invalidate old cache entries
    await cache.delete(get_user_cache_key(db_user.id))
    await cache.delete(get_user_email_cache_key(old_email))

    # If email changed, invalidate old email cache
    if "email" in update_data and update_data["email"] != old_email:
        await cache.delete(get_user_email_cache_key(old_email))

    # Cache updated user data
    user_dict = {
        "id": db_user.id,
        "email": db_user.email,
        "first_name": db_user.first_name,
        "last_name": db_user.last_name,
        "hashed_password": db_user.hashed_password,
        "status": db_user.status,
        "action": db_user.action,
        "login_token": db_user.login_token,
        "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
        "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
    }
    await cache.set(get_user_cache_key(db_user.id), user_dict)
    await cache.set(get_user_email_cache_key(db_user.email), user_dict)

    # Publish user updated event
    await publish_user_updated_event(
        user_id=db_user.id,
        updated_fields=updated_fields,
        email=db_user.email if "email" in updated_fields else None,
        first_name=db_user.first_name if "first_name" in updated_fields else None,
        last_name=db_user.last_name if "last_name" in updated_fields else None,
        status=db_user.status if "status" in updated_fields else None,
        action=db_user.action if "action" in updated_fields else None
    )

    return db_user

async def update_user_by_email(db: Session, email: str, user: UserUpdate) -> User:
    """Update a user by email"""
    db_user = await get_user_by_email(db, email=email)
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    update_data = user.model_dump(exclude_unset=True)
    updated_fields = list(update_data.keys())

    # Hash the password if it's being updated
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data["password"])
        del update_data["password"]
        updated_fields.append("hashed_password")

    # Store old email for cache invalidation
    old_email = db_user.email

    # Check email uniqueness if being updated
    if "email" in update_data and update_data["email"] != db_user.email:
        if await get_user_by_email(db, email=update_data["email"]):
            raise HTTPException(status_code=400, detail="Email already registered")

    # No encrypted fields in current User model

    for key, value in update_data.items():
        setattr(db_user, key, value)

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Invalidate old cache entries
    await cache.delete(get_user_cache_key(db_user.id))
    await cache.delete(get_user_email_cache_key(old_email))

    # If email changed, invalidate old email cache
    if "email" in update_data and update_data["email"] != old_email:
        await cache.delete(get_user_email_cache_key(old_email))

    # Cache updated user data
    user_dict = {
        "id": db_user.id,
        "email": db_user.email,
        "first_name": db_user.first_name,
        "last_name": db_user.last_name,
        "hashed_password": db_user.hashed_password,
        "status": db_user.status,
        "action": db_user.action,
        "login_token": db_user.login_token,
        "created_at": db_user.created_at.isoformat() if db_user.created_at else None,
        "updated_at": db_user.updated_at.isoformat() if db_user.updated_at else None
    }
    await cache.set(get_user_cache_key(db_user.id), user_dict)
    await cache.set(get_user_email_cache_key(db_user.email), user_dict)

    # Publish user updated event
    await publish_user_updated_event(
        user_id=db_user.id,
        updated_fields=updated_fields,
        email=db_user.email if "email" in updated_fields else None,
        first_name=db_user.first_name if "first_name" in updated_fields else None,
        last_name=db_user.last_name if "last_name" in updated_fields else None,
        status=db_user.status if "status" in updated_fields else None,
        action=db_user.action if "action" in updated_fields else None
    )

    return db_user

async def delete_user(db: Session, user_id: int) -> None:
    """Delete a user by ID"""
    db_user = await get_user(db, user_id=user_id)
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Store email before deletion for event
    user_email = db_user.email

    db.delete(db_user)
    db.commit()

    # Invalidate cache entries
    await cache.delete(get_user_cache_key(user_id))
    await cache.delete(get_user_email_cache_key(user_email))
    await cache.delete(get_login_token_cache_key(user_id))

    # Publish user deleted event
    await publish_user_deleted_event(
        user_id=user_id,
        email=user_email
    )

async def delete_user_by_email(db: Session, email: str) -> None:
    """Delete a user by email"""
    db_user = await get_user_by_email(db, email=email)
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Store user_id before deletion for event
    user_id = db_user.id

    db.delete(db_user)
    db.commit()

    # Invalidate cache entries
    await cache.delete(get_user_cache_key(user_id))
    await cache.delete(get_user_email_cache_key(email))
    await cache.delete(get_login_token_cache_key(user_id))

    # Publish user deleted event
    await publish_user_deleted_event(
        user_id=user_id,
        email=email
    )

async def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    """Authenticate a user by email and password"""
    user = await get_user_by_email(db, email=email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

async def save_login_token(db: Session, user_id: int, token: str) -> bool:
    """Save the login token for a user"""
    try:
        user = await get_user(db, user_id=user_id)
        if not user:
            return False

        user.login_token = token
        db.commit()
        db.refresh(user)

        # Invalidate user cache to force refresh
        await cache.delete(get_user_cache_key(user_id))
        await cache.delete(get_user_email_cache_key(user.email))

        # Cache the login token separately
        await cache.set(get_login_token_cache_key(user_id), token)

        return True
    except Exception:
        db.rollback()
        return False

def get_user_with_decrypted_fields(db: Session, user_id: int) -> Optional[UserResponse]:
    """Get a user by ID (no encrypted fields in current model)"""
    user = get_user(db, user_id=user_id)
    if not user:
        return None

    return UserResponse(
        id=user.id,
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        status=user.status,
        action=user.action,
        created_at=user.created_at,
        updated_at=user.updated_at
    )

def get_user_with_decrypted_fields_by_email(db: Session, email: str) -> Optional[UserResponse]:
    """Get a user by email (no encrypted fields in current model)"""
    user = get_user_by_email(db, email=email)
    if not user:
        return None

    return UserResponse(
        id=user.id,
        email=user.email,
        first_name=user.first_name,
        last_name=user.last_name,
        status=user.status,
        action=user.action,
        created_at=user.created_at,
        updated_at=user.updated_at
    )

async def update_user_password(db: Session, user_id: int, new_password: str) -> bool:
    """Update user password by user ID"""
    try:
        # Get user directly from database (not cache) to ensure it's attached to session
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return False

        user.hashed_password = get_password_hash(new_password)
        db.commit()
        db.refresh(user)

        # Invalidate cache after password update (optional, don't fail if cache fails)
        try:
            await cache.delete(get_user_cache_key(user_id))
            await cache.delete(get_user_email_cache_key(user.email))
        except Exception as cache_error:
            # Log cache error but don't fail the password update
            logging.warning(f"Failed to invalidate cache after password update: {cache_error}")

        return True
    except Exception as e:
        logging.error(f"Error updating password for user {user_id}: {str(e)}")
        db.rollback()
        return False

async def update_user_password_by_email(db: Session, email: str, new_password: str) -> bool:
    """Update user password by email"""
    try:
        # Get user directly from database (not cache) to ensure it's attached to session
        user = db.query(User).filter(User.email == email).first()
        if not user:
            return False

        user.hashed_password = get_password_hash(new_password)
        db.commit()
        db.refresh(user)

        # Invalidate cache after password update (optional, don't fail if cache fails)
        try:
            await cache.delete(get_user_cache_key(user.id))
            await cache.delete(get_user_email_cache_key(email))
        except Exception as cache_error:
            # Log cache error but don't fail the password update
            logging.warning(f"Failed to invalidate cache after password update: {cache_error}")

        return True
    except Exception as e:
        logging.error(f"Error updating password for {email}: {str(e)}")
        db.rollback()
        return False
