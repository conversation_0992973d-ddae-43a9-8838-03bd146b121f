routes:
  - uri: /auth/docs*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      - proxy-rewrite:
          regex_uri: ["^/auth/docs/(.*)", "/docs/$1"]
  - uri: /auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      - jwt-auth:
          query: token
  # Auth service API routes via APISIX
  - uri: /api/v1/auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
      checks:
        active:
          type: http
          http_path: "/health"
          healthy:
            interval: 30
            http_statuses: [200, 302]
            successes: 2
          unhealthy:
            interval: 30
            http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
            http_failures: 3
            timeouts: 3
    plugins:
      cors:
        allow_origins: "http://localhost,https://localhost"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      limit-req:
        rate: 5
        burst: 20
        rejected_code: 429
        nodelay: true
      proxy-rewrite:
        regex_uri: ["^/api/v1/auth/(.*)", "/api/v1/$1"]

  # Swagger UI and OpenAPI Documentation routes
  - uri: /docs
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "*"
        expose_headers: "*"
        allow_credential: true

  - uri: /redoc
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET"
        allow_headers: "*"

  - uri: /openapi.json
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      cors:
        allow_origins: "*"
        allow_methods: "GET"
        allow_headers: "*"

  # Direct auth service routes
  - uri: /auth/*
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
      checks:
        active:
          type: http
          http_path: "/health"
          healthy:
            interval: 30
            http_statuses: [200, 302]
            successes: 2
          unhealthy:
            interval: 30
            http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
            http_failures: 3
            timeouts: 3
    plugins:
      cors:
        allow_origins: "http://localhost,https://localhost"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      limit-req:
        rate: 5
        burst: 20
        rejected_code: 429
        nodelay: true
      proxy-rewrite:
        regex_uri: ["^/auth/(.*)", "/$1"]

  # Health check route
  - uri: /health
    plugins:
      echo:
        body: "APISIX Auth Gateway - healthy"

  # Root route for auth gateway
  - uri: /
    plugins:
      echo:
        body: '{"message": "ZCare Auth Gateway - APISIX", "version": "1.0.0", "service": "auth-service", "port": 8080}'

  # Swagger UI and OpenAPI schema routes
  - uri: /api/v1/docs
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      cors:
        allow_origins: "http://localhost,https://localhost"
        allow_methods: "GET,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
      proxy-rewrite:
        regex_uri: ["^/api/v1/docs$", "/docs"]
  - uri: /api/v1/openapi.json
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      cors:
        allow_origins: "http://localhost,https://localhost"
        allow_methods: "GET,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
  - uri: /docs
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      cors:
        allow_origins: "http://localhost,https://localhost"
        allow_methods: "GET,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600
  - uri: /docs/
    upstream:
      type: roundrobin
      nodes:
        "auth-service:8000": 1
    plugins:
      cors:
        allow_origins: "http://localhost,https://localhost"
        allow_methods: "GET,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600

upstreams:
  - id: auth-service
    name: "Auth Service Upstream"
    type: roundrobin
    nodes:
      "auth-service:8000": 1
    checks:
      active:
        type: http
        http_path: "/health"
        healthy:
          interval: 30
          http_statuses: [200, 302]
          successes: 2
        unhealthy:
          interval: 30
          http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
          http_failures: 3
          timeouts: 3

global_rules:
  - id: global-cors-auth
    plugins:
      cors:
        allow_origins: "http://localhost,https://localhost"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
        expose_headers: "Content-Length,Content-Range"
        allow_credential: true
        max_age: 3600

#END
