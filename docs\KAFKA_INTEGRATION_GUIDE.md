# ZCare Platform - Kafka Integration Guide

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- Git (for cloning the repository)
- Python 3.7+ (for testing scripts)

### 1. Start the Application

**Option A: Using the automated script**
```bash
run-app.bat
```

**Option B: Manual step-by-step**
```bash
# Stop any existing containers
docker-compose down

# Build the services
docker-compose build

# Start infrastructure services first
docker-compose up -d postgres-admin postgres-auth zookeeper kafka

# Wait 30 seconds for infrastructure to be ready
# Then start application services
docker-compose up -d auth-service admin-service

# Wait 20 seconds for services to start
# Then start the gateway
docker-compose up -d kong

# Optional: Start observability services
docker-compose up -d prometheus grafana
```

### 2. Verify Services

**Check service status:**
```bash
check-services.bat
```

**Or manually:**
```bash
docker-compose ps
```

## 🌐 Service Endpoints

| Service | Direct Access | Via Gateway |
|---------|---------------|-------------|
| Auth Service | http://localhost:8002 | http://localhost:8000/auth |
| Admin Service | http://localhost:8001 | http://localhost:8000/admin |
| Kong Gateway | http://localhost:8000 | - |
| Kong Admin | http://localhost:8444 | - |
| Prometheus | http://localhost:9090 | - |
| Grafana | http://localhost:3000 | - |

## 🔧 Kafka Integration

### What's Implemented

1. **Real Kafka Clients**: Both services now use `aiokafka` for real Kafka integration
2. **Event Producers**: 
   - Auth Service: User creation, login events
   - Admin Service: Domain creation, application creation events
3. **Event Consumers**: 
   - Admin Service: Consumes domain and application events
4. **Fallback Mechanism**: Services gracefully fall back to no-op implementations if Kafka is unavailable

### Kafka Topics

- `user-events`: User-related events from auth service
- `domain-events`: Domain-related events from admin service  
- `application-events`: Application-related events from admin service

### Testing Kafka Integration

**Run the automated test:**
```bash
python test-kafka-integration.py
```

**Manual Kafka testing:**
```bash
kafka-tools.bat
```

### Kafka Management Commands

**List topics:**
```bash
docker-compose exec kafka kafka-topics --list --bootstrap-server localhost:9092
```

**Create a topic:**
```bash
docker-compose exec kafka kafka-topics --create --topic test-topic --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1
```

**Send a message:**
```bash
echo "test message" | docker-compose exec -T kafka kafka-console-producer --topic test-topic --bootstrap-server localhost:9092
```

**Consume messages:**
```bash
docker-compose exec kafka kafka-console-consumer --topic test-topic --from-beginning --bootstrap-server localhost:9092
```

## 🧪 Testing the Integration

### 1. Test User Creation (Triggers Kafka Event)

```bash
curl -X POST http://localhost:8002/api/v1/users/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User", 
    "password": "testpassword123"
  }'
```

### 2. Test Domain Creation (Triggers Kafka Event)

```bash
curl -X POST http://localhost:8001/api/v1/domains/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-domain",
    "description": "Test domain for Kafka"
  }'
```

### 3. Monitor Kafka Events

```bash
# Monitor user events
docker-compose exec kafka kafka-console-consumer --topic user-events --from-beginning --bootstrap-server localhost:9092

# Monitor domain events  
docker-compose exec kafka kafka-console-consumer --topic domain-events --from-beginning --bootstrap-server localhost:9092
```

## 🔍 Troubleshooting

### Common Issues

1. **Services not starting**: Check if Docker Desktop is running
2. **Port conflicts**: Make sure ports 8000-8002, 8444, 9092, 5433-5434, 2181 are available
3. **Kafka connection issues**: Check if Kafka container is healthy

### Useful Commands

**View service logs:**
```bash
docker-compose logs auth-service
docker-compose logs admin-service
docker-compose logs kafka
```

**Restart a specific service:**
```bash
docker-compose restart auth-service
```

**Clean restart:**
```bash
docker-compose down -v
docker-compose up -d
```

## 📊 Monitoring

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)
- **Service Logs**: `docker-compose logs [service-name]`

## 🔧 Configuration

### Environment Variables

Key environment variables in `docker-compose.yml`:
- `KAFKA_BOOTSTRAP_SERVERS`: Kafka connection string
- `JWT_SECRET_KEY`: JWT signing key
- `RSA_PRIVATE_KEY` / `RSA_PUBLIC_KEY`: RSA keys for hybrid encryption

### Kafka Configuration

Kafka is configured with:
- Auto topic creation enabled
- Single broker setup (suitable for development)
- Zookeeper for coordination
- Health checks enabled

## 🎯 Next Steps

1. **Scale Services**: Add more replicas using `docker-compose up --scale admin-service=2`
2. **Add More Events**: Implement additional event types
3. **Event Sourcing**: Consider implementing event sourcing patterns
4. **Monitoring**: Set up alerts and dashboards in Grafana
5. **Production Setup**: Configure Kafka cluster for production use
