_format_version: "2.1"

services:
- name: admin-service
  url: http://admin-service:8000
  routes:
  - name: admin-api
    paths:
    - /api/v1/admin
    strip_path: true
    plugins:
    - name: jwt
      config:
        claims_to_verify:
        - exp
    - name: rate-limiting
      config:
        minute: 60
        policy: local
    - name: cors
      config:
        origins:
        - '*'
        methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
        - PATCH
        headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
        exposed_headers:
        - X-Auth-Token
        credentials: true
        max_age: 3600
        preflight_continue: false
    - name: custom-auth
      config:
        header_name: Authorization
  - name: admin-docs
    paths:
    - /api/v1/admin/docs
    strip_path: false
    preserve_host: true
  plugins:
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      upstream_health_metrics: true
      bandwidth_metrics: true
