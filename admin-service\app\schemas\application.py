from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from app.regex.regex_patterns import APPLICATION_NAME_REGEX, APPLICATION_CODE_REGEX, APPLICATION_DESCRIPTION_REGEX,DOMAIN_NAME_REGEX

class ApplicationBase(BaseModel):
    application_name: str = Field(..., pattern=APPLICATION_NAME_REGEX)  # Updated from `name`
    application_code: str = Field(..., pattern=APPLICATION_CODE_REGEX)   # Added as `Application code`
    description: Optional[str] = Field(None, pattern=APPLICATION_DESCRIPTION_REGEX)  # Remains unchanged
    status: Optional[bool] = True  # Renamed from `is_active`
    action: Optional[str] = None  # Added `action`
    domain_id: int = Field(..., description="Domain ID foreign key")  # Added domain_id field
    domain_name: str = Field(..., pattern=DOMAIN_NAME_REGEX)  # Keep for backward compatibility
    config: Optional[str] = None  # JSON stored as text

class ApplicationCreate(ApplicationBase):
    pass  # No changes required

class ApplicationUpdate(BaseModel):
    application_name: Optional[str] = Field(None, pattern=APPLICATION_NAME_REGEX)  # Updated from `name`
    application_code: Optional[str] = Field(None, pattern=APPLICATION_CODE_REGEX)   # Added as `Application code`
    description: Optional[str] = Field(None, pattern=APPLICATION_DESCRIPTION_REGEX)  # Remains unchanged
    status: Optional[bool] = None  # Updated from `is_active`
    action: Optional[str] = None  # Added `action`
    domain_id: Optional[int] = Field(None, description="Domain ID foreign key")  # Added domain_id field
    domain_name: Optional[str] = Field(None, pattern=DOMAIN_NAME_REGEX)  # Keep for backward compatibility
    config: Optional[str] = None  # JSON stored as text

class ApplicationResponse(ApplicationBase):
    id: int  # No changes
    created_at: datetime  # No changes
    updated_at: Optional[datetime] = None  # No changes

    class Config:
        from_attributes = True  # Updated for Pydantic v2
