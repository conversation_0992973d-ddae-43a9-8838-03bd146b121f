from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import asyncio
from app.api.routes import domain, application, encryption, kafka_health
from app.core.config import settings
from app.db.session import engine
from app.db.base import Base
from app.events.consumers import domain_events, application_events
from app.events.kafka_client import KAFKA_ENABLED

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="Admin Service API for ZCare Platform",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    openapi_version="3.0.2"
)

# Set CORS middleware for development
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(domain.router, prefix=settings.API_V1_STR)
app.include_router(application.router, prefix=settings.API_V1_STR)
app.include_router(encryption.router, prefix=settings.API_V1_STR)
app.include_router(kafka_health.router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    return {"message": "Welcome to ZCare Admin Service"}

@app.get("/health")
async def health():
    return {"status": "ok"}

# Event consumers startup and shutdown
@app.on_event("startup")
async def startup_event_consumers():
    print("Starting admin service startup process...")

    if KAFKA_ENABLED:
        try:
            # Start domain events consumer
            app.state.domain_consumer_task = asyncio.create_task(
                domain_events.start_consumer()
            )

            # Start application events consumer
            app.state.application_consumer_task = asyncio.create_task(
                application_events.start_consumer()
            )

            app.state.event_consumers_running = True
            print("Kafka event consumers started successfully")
        except Exception as e:
            print(f"Warning: Failed to start Kafka consumers: {str(e)}")
            app.state.event_consumers_running = False
    else:
        print("Kafka is disabled - event consumers not started")
        app.state.event_consumers_running = False

    print("Application startup complete")

@app.on_event("shutdown")
async def shutdown_event_consumers():
    # Cancel event consumer tasks and close Kafka producer
    try:
        if hasattr(app.state, 'event_consumers_running') and app.state.event_consumers_running:
            # Cancel the background tasks
            if hasattr(app.state, 'domain_consumer_task'):
                app.state.domain_consumer_task.cancel()
                try:
                    await app.state.domain_consumer_task
                except asyncio.CancelledError:
                    pass

            if hasattr(app.state, 'application_consumer_task'):
                app.state.application_consumer_task.cancel()
                try:
                    await app.state.application_consumer_task
                except asyncio.CancelledError:
                    pass

            app.state.event_consumers_running = False
            print("Event consumer tasks cancelled")

        # Close Kafka producer
        from app.events.kafka_client import close_producer
        await close_producer()
        print("Kafka producer closed")

    except Exception as e:
        print(f"Error during shutdown: {str(e)}")
        print("Application will continue to shut down")

# Remove duplicate __main__ block and ensure only one entry point
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
