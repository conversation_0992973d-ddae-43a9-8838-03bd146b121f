"""add domain_id to applications table

Revision ID: 003
Revises: 002
Create Date: 2024-06-19

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade():
    # Add domain_id column to applications table
    op.add_column('applications', sa.Column('domain_id', sa.Integer(), nullable=True))
    
    # Create foreign key constraint for domain_id
    op.create_foreign_key(
        'fk_applications_domain_id',
        'applications', 'domains',
        ['domain_id'], ['id'],
        ondelete='CASCADE'
    )
    
    # Create index for domain_id
    op.create_index('ix_applications_domain_id', 'applications', ['domain_id'])
    
    # Update existing records to populate domain_id based on domain_name
    # This requires a data migration
    connection = op.get_bind()
    connection.execute(sa.text("""
        UPDATE applications 
        SET domain_id = (
            SELECT id FROM domains 
            WHERE domains.domain_name = applications.domain_name
        )
        WHERE domain_name IS NOT NULL
    """))
    
    # Make domain_id NOT NULL after populating data
    op.alter_column('applications', 'domain_id', nullable=False)


def downgrade():
    # Drop index
    op.drop_index('ix_applications_domain_id', table_name='applications')
    
    # Drop foreign key constraint
    op.drop_constraint('fk_applications_domain_id', 'applications', type_='foreignkey')
    
    # Drop domain_id column
    op.drop_column('applications', 'domain_id')
