import os
from typing import List
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig, MessageType
from pydantic import EmailStr
import logging

logger = logging.getLogger(__name__)

# Email configuration
conf = ConnectionConfig(
    MAIL_USERNAME=os.getenv("MAIL_USERNAME", "Zionix"),
    MAIL_PASSWORD=os.getenv("MAIL_PASSWORD", "Arun@1234"),
    MAIL_FROM=os.getenv("MAIL_FROM", "<EMAIL>"),
    MAIL_PORT=int(os.getenv("MAIL_PORT", "587")),
    MAIL_SERVER=os.getenv("MAIL_SERVER", "smtp.gmail.com"),
    MAIL_FROM_NAME=os.getenv("MAIL_FROM_NAME", "Zionix Auth Service"),
    MAIL_STARTTLS=True,
    MAIL_SSL_TLS=False,
    USE_CREDENTIALS=True,
    VALIDATE_CERTS=True
)

fastmail = FastMail(conf)

async def send_otp_email(email: EmailStr, otp_code: str, user_name: str = "User") -> bool:
    """Send OTP email for password reset"""
    try:
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Password Reset OTP</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    background-color: #007bff;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 5px 5px 0 0;
                }}
                .content {{
                    background-color: #f8f9fa;
                    padding: 30px;
                    border-radius: 0 0 5px 5px;
                }}
                .otp-code {{
                    background-color: #007bff;
                    color: white;
                    font-size: 24px;
                    font-weight: bold;
                    padding: 15px;
                    text-align: center;
                    border-radius: 5px;
                    margin: 20px 0;
                    letter-spacing: 3px;
                }}
                .warning {{
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    color: #666;
                    font-size: 14px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Password Reset Request</h1>
            </div>
            <div class="content">
                <h2>Hello {user_name},</h2>
                <p>We received a request to reset your password. Please use the following One-Time Password (OTP) to proceed with your password reset:</p>
                
                <div class="otp-code">
                    {otp_code}
                </div>
                
                <div class="warning">
                    <strong>Important:</strong>
                    <ul>
                        <li>This OTP is valid for only <strong>5 minutes</strong></li>
                        <li>Do not share this code with anyone</li>
                        <li>If you didn't request this password reset, please ignore this email</li>
                    </ul>
                </div>
                
                <p>If you have any questions or concerns, please contact our support team.</p>
                
                <p>Best regards,<br>
                The Zionix Team</p>
            </div>
            <div class="footer">
                <p>This is an automated message. Please do not reply to this email.</p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        Password Reset Request
        
        Hello {user_name},
        
        We received a request to reset your password. Please use the following One-Time Password (OTP) to proceed with your password reset:
        
        OTP: {otp_code}
        
        Important:
        - This OTP is valid for only 5 minutes
        - Do not share this code with anyone
        - If you didn't request this password reset, please ignore this email
        
        If you have any questions or concerns, please contact our support team.
        
        Best regards,
        The Zionix Team
        
        This is an automated message. Please do not reply to this email.
        """

        message = MessageSchema(
            subject="Password Reset OTP - Zionix",
            recipients=[email],
            body=text_content,
            html=html_content,
            subtype=MessageType.html
        )

        await fastmail.send_message(message)
        logger.info(f"OTP email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send OTP email to {email}: {str(e)}")
        return False

async def send_password_reset_confirmation_email(email: EmailStr, user_name: str = "User") -> bool:
    """Send confirmation email after successful password reset"""
    try:
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Password Reset Successful</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    background-color: #28a745;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 5px 5px 0 0;
                }}
                .content {{
                    background-color: #f8f9fa;
                    padding: 30px;
                    border-radius: 0 0 5px 5px;
                }}
                .success {{
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    color: #155724;
                    padding: 15px;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    color: #666;
                    font-size: 14px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Password Reset Successful</h1>
            </div>
            <div class="content">
                <h2>Hello {user_name},</h2>
                
                <div class="success">
                    <strong>Success!</strong> Your password has been reset successfully.
                </div>
                
                <p>Your account password has been updated. You can now log in with your new password.</p>
                
                <p>If you did not make this change, please contact our support team immediately.</p>
                
                <p>For your security, we recommend:</p>
                <ul>
                    <li>Using a strong, unique password</li>
                    <li>Not sharing your password with anyone</li>
                    <li>Logging out from shared devices</li>
                </ul>
                
                <p>Best regards,<br>
                The Zionix Team</p>
            </div>
            <div class="footer">
                <p>This is an automated message. Please do not reply to this email.</p>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        Password Reset Successful
        
        Hello {user_name},
        
        Success! Your password has been reset successfully.
        
        Your account password has been updated. You can now log in with your new password.
        
        If you did not make this change, please contact our support team immediately.
        
        For your security, we recommend:
        - Using a strong, unique password
        - Not sharing your password with anyone
        - Logging out from shared devices
        
        Best regards,
        The Zionix Team
        
        This is an automated message. Please do not reply to this email.
        """

        message = MessageSchema(
            subject="Password Reset Successful - Zionix",
            recipients=[email],
            body=text_content,
            html=html_content,
            subtype=MessageType.html
        )

        await fastmail.send_message(message)
        logger.info(f"Password reset confirmation email sent successfully to {email}")
        return True

    except Exception as e:
        logger.error(f"Failed to send password reset confirmation email to {email}: {str(e)}")
        return False