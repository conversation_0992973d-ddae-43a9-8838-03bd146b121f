@echo off
echo 🔐 Testing Password Reset Functionality
echo =======================================

echo.
echo 1. Creating a test user for password reset...
echo.

powershell -Command "Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/users/' -Method POST -ContentType 'application/json' -Body '{\"email\":\"<EMAIL>\",\"first_name\":\"Password\",\"last_name\":\"Test\",\"password\":\"OldPassword123!\",\"confirm_password\":\"OldPassword123!\"}'"

echo.
echo 2. Initiating forgot password request...
echo.

powershell -Command "Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/forgot-password' -Method POST -ContentType 'application/json' -Body '{\"email\":\"<EMAIL>\"}'"

echo.
echo 3. Checking if OTP was created in database...
echo.

docker exec zionix-be-v1-postgres-auth-1 psql -U postgres -d auth_db -c "SELECT email, otp_code, is_used, expires_at FROM otps WHERE email = '<EMAIL>' ORDER BY created_at DESC LIMIT 1;"

echo.
echo 4. Getting the OTP code for testing...
echo.

for /f "tokens=2" %%i in ('docker exec zionix-be-v1-postgres-auth-1 psql -U postgres -d auth_db -t -c "SELECT otp_code FROM otps WHERE email = ''<EMAIL>'' AND is_used = false ORDER BY created_at DESC LIMIT 1;"') do set OTP_CODE=%%i

echo OTP Code: %OTP_CODE%

echo.
echo 5. Verifying OTP...
echo.

powershell -Command "Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/verify-otp' -Method POST -ContentType 'application/json' -Body '{\"email\":\"<EMAIL>\",\"otp_code\":\"%OTP_CODE%\"}'"

echo.
echo 6. Resetting password with verified OTP...
echo.

powershell -Command "try { Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/reset-password' -Method POST -ContentType 'application/json' -Body '{\"email\":\"<EMAIL>\",\"otp_code\":\"%OTP_CODE%\",\"new_password\":\"NewPassword123!\",\"confirm_password\":\"NewPassword123!\"}' } catch { Write-Host 'Error:' $_.Exception.Message }"

echo.
echo 7. Testing login with new password...
echo.

powershell -Command "try { Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/login' -Method POST -ContentType 'application/x-www-form-urlencoded' -Body 'username=<EMAIL>&password=NewPassword123!' } catch { Write-Host 'Login Error:' $_.Exception.Message }"

echo.
echo 🎉 Password reset test completed!
echo.

pause
