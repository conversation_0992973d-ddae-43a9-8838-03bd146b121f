"""remove encrypted fields from users table

Revision ID: 006
Revises: 005
Create Date: 2025-07-14 14:30:00

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '006'
down_revision = '005'
branch_labels = None
depends_on = None


def upgrade():
    # Remove encrypted fields from users table
    op.drop_column('users', 'encrypted_phone')
    op.drop_column('users', 'encrypted_address')
    op.drop_column('users', 'encrypted_notes')


def downgrade():
    # Add back encrypted fields (in case rollback is needed)
    op.add_column('users', sa.Column('encrypted_phone', sa.Text(), nullable=True))
    op.add_column('users', sa.Column('encrypted_address', sa.Text(), nullable=True))
    op.add_column('users', sa.Column('encrypted_notes', sa.Text(), nullable=True))
