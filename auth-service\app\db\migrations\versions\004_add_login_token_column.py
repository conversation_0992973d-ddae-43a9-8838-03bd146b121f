"""add login_token column to users table

Revision ID: 004
Revises: 003
Create Date: 2024-01-16

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade():
    # Add login_token column to users table
    op.add_column('users', sa.Column('login_token', sa.Text(), nullable=True))


def downgrade():
    # Remove login_token column from users table
    op.drop_column('users', 'login_token')
