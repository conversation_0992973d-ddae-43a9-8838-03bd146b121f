@echo off
echo Testing Auth Service Redis Integration
echo =====================================

echo.
echo 1. Testing auth-service health...
curl -s http://localhost:8002/health
echo.

echo.
echo 2. Checking current Redis keys before API call...
docker exec zionix-be-v1-redis-1 redis-cli KEYS "user:*"
echo.

echo.
echo 3. Testing user registration (this should trigger Redis caching)...
curl -X POST "http://localhost:8002/api/v1/users/" ^
  -H "Content-Type: application/json" ^
  -d "{\"email\":\"<EMAIL>\",\"first_name\":\"Test\",\"last_name\":\"User\",\"password\":\"TestPassword123!\",\"confirm_password\":\"TestPassword123!\"}"

echo.
echo.
echo 4. Checking Redis keys after user registration...
docker exec zionix-be-v1-redis-1 redis-cli KEYS "user:*"
echo.

echo.
echo 5. Testing user login (this should use Redis cache)...
curl -X POST "http://localhost:8002/api/v1/auth/login" ^
  -H "Content-Type: application/x-www-form-urlencoded" ^
  -d "username=<EMAIL>&password=TestPassword123!"

echo.
echo.
echo 6. Final Redis keys check...
docker exec zionix-be-v1-redis-1 redis-cli KEYS "*"
echo.

echo.
echo ✅ Redis integration test completed!
echo 📋 Check your Redis browser to see the cached user data.

pause
