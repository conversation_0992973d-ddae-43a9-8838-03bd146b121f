"""Create OTPs table

Revision ID: 005
Revises: 004
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '005'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade():
    # Create OTPs table
    op.create_table('otps',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('otp_code', sa.String(length=6), nullable=False),
        sa.Column('purpose', sa.String(length=50), nullable=False),
        sa.Column('is_used', sa.Boolean(), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index(op.f('ix_otps_id'), 'otps', ['id'], unique=False)
    op.create_index('ix_otps_user_id_purpose', 'otps', ['user_id', 'purpose'], unique=False)
    op.create_index('ix_otps_expires_at', 'otps', ['expires_at'], unique=False)


def downgrade():
    # Drop indexes
    op.drop_index('ix_otps_expires_at', table_name='otps')
    op.drop_index('ix_otps_user_id_purpose', table_name='otps')
    op.drop_index(op.f('ix_otps_id'), table_name='otps')
    
    # Drop table
    op.drop_table('otps')