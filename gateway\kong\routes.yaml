apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: admin-service-ingress
  namespace: zionix
  annotations:
    kubernetes.io/ingress.class: kong
    konghq.com/plugins: jwt-auth, prometheus-metrics
spec:
  rules:
    - host: api.zionix.local
      http:
        paths:
          - path: /admin
            pathType: Prefix
            backend:
              service:
                name: admin-service
                port:
                  number: 8003
                  
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: auth-service-ingress
  namespace: zionix
  annotations:
    kubernetes.io/ingress.class: kong
    konghq.com/plugins: prometheus-metrics
spec:
  rules:
    - host: api.zionix.local
      http:
        paths:
          - path: /auth
            pathType: Prefix
            backend:
              service:
                name: auth-service
                port:
                  number: 8002
