from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class UserEventBase(BaseModel):
    """Base schema for user events"""
    event_type: str
    user_id: int
    timestamp: datetime
    
class UserCreatedEvent(UserEventBase):
    """Event schema for user creation"""
    event_type: str = "user_created"
    email: EmailStr
    first_name: str
    last_name: str
    status: bool
    action: Optional[str] = None

class UserUpdatedEvent(UserEventBase):
    """Event schema for user updates"""
    event_type: str = "user_updated"
    email: Optional[EmailStr] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    status: Optional[bool] = None
    action: Optional[str] = None
    updated_fields: list[str]

class UserDeletedEvent(UserEventBase):
    """Event schema for user deletion"""
    event_type: str = "user_deleted"
    email: EmailStr

class UserLoginEvent(UserEventBase):
    """Event schema for user login"""
    event_type: str = "user_login"
    email: EmailStr
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class UserLogoutEvent(UserEventBase):
    """Event schema for user logout"""
    event_type: str = "user_logout"
    email: EmailStr
    session_duration: Optional[int] = None  # in seconds

class UserPasswordChangedEvent(UserEventBase):
    """Event schema for password changes"""
    event_type: str = "user_password_changed"
    email: EmailStr
