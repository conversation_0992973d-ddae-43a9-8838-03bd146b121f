_format_version: "2.1"

services:
- name: auth-service
  url: http://auth-service:8000
  routes:
  - name: auth-api
    paths:
    - /api/v1/auth
    strip_path: true
    plugins:
    - name: cors
      config:
        origins:
        - '*'
        methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
        - PATCH
        headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
        exposed_headers:
        - X-Auth-Token
        credentials: true
        max_age: 3600
        preflight_continue: false
    - name: rate-limiting
      config:
        minute: 120
        policy: local
  plugins:
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      upstream_health_metrics: true
      bandwidth_metrics: true